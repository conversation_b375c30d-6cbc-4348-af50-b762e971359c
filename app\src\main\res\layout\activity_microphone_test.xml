<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/robot_background">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎤 Microphone Test for Android 15"
        android:textSize="24sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Status Display -->
    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Initializing..."
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:background="@drawable/settings_section_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/start_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎤 Start Test"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginEnd="8dp"
            android:enabled="false" />

        <Button
            android:id="@+id/stop_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="⏹️ Stop"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginStart="8dp"
            android:enabled="false" />

    </LinearLayout>

    <!-- Clear Button -->
    <Button
        android:id="@+id/clear_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🧹 Clear Transcription"
        android:textColor="@color/white"
        android:background="@drawable/control_button_background"
        android:layout_marginBottom="16dp" />

    <!-- Instructions -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📋 Instructions:\n• Tap 'Start Test' to begin\n• Speak clearly into the microphone\n• Your speech will appear below in real-time\n• This tests microphone hardware and speech recognition"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:background="@drawable/settings_section_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Live Transcription Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📝 Live Transcription:"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- Transcription Display -->
    <ScrollView
        android:id="@+id/transcription_scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/settings_section_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/transcription_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Transcription will appear here...\n\nThis is a standalone test that doesn't interfere with the main app's voice system."
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:fontFamily="monospace"
            android:lineSpacingExtra="4dp" />

    </ScrollView>

    <!-- Footer Info -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🔧 This is a diagnostic tool for Android 15 microphone testing"
        android:textSize="12sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginTop="8dp" />

</LinearLayout>
