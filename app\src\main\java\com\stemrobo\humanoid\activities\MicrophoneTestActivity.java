package com.stemrobo.humanoid.activities;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.stemrobo.humanoid.R;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;

/**
 * Standalone Microphone Test Activity for Android 15
 * Tests microphone functionality with live transcription
 */
public class MicrophoneTestActivity extends AppCompatActivity implements RecognitionListener {
    private static final String TAG = "MicrophoneTest";
    private static final int MICROPHONE_PERMISSION_REQUEST = 1001;
    
    // UI Components
    private TextView statusText;
    private TextView transcriptionText;
    private ScrollView transcriptionScroll;
    private Button startButton;
    private Button stopButton;
    private Button clearButton;
    
    // Speech Recognition
    private SpeechRecognizer speechRecognizer;
    private Intent recognizerIntent;
    private boolean isListening = false;
    private Handler handler;
    
    // Audio Manager
    private AudioManager audioManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_microphone_test);
        
        Log.d(TAG, "🎤 MicrophoneTestActivity created for Android " + android.os.Build.VERSION.SDK_INT);
        
        initializeViews();
        initializeAudio();
        checkPermissions();
        
        handler = new Handler(Looper.getMainLooper());
    }
    
    private void initializeViews() {
        statusText = findViewById(R.id.status_text);
        transcriptionText = findViewById(R.id.transcription_text);
        transcriptionScroll = findViewById(R.id.transcription_scroll);
        startButton = findViewById(R.id.start_button);
        stopButton = findViewById(R.id.stop_button);
        clearButton = findViewById(R.id.clear_button);
        
        // Set up button listeners
        startButton.setOnClickListener(v -> startListening());
        stopButton.setOnClickListener(v -> stopListening());
        clearButton.setOnClickListener(v -> clearTranscription());
        
        // Initial UI state
        updateStatus("Initializing microphone test...");
        stopButton.setEnabled(false);
    }
    
    private void initializeAudio() {
        try {
            audioManager = (AudioManager) getSystemService(AUDIO_SERVICE);
            
            // Log device and audio info
            logDeviceInfo();
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error initializing audio", e);
            updateStatus("❌ Error initializing audio: " + e.getMessage());
        }
    }
    
    private void logDeviceInfo() {
        String manufacturer = android.os.Build.MANUFACTURER;
        String model = android.os.Build.MODEL;
        int sdkVersion = android.os.Build.VERSION.SDK_INT;
        String release = android.os.Build.VERSION.RELEASE;
        
        String deviceInfo = "📱 Device: " + manufacturer + " " + model + "\n" +
                           "🤖 Android: " + release + " (API " + sdkVersion + ")\n";
        
        // Check microphone hardware
        boolean hasMicrophone = getPackageManager().hasSystemFeature(PackageManager.FEATURE_MICROPHONE);
        deviceInfo += "🎤 Hardware Microphone: " + (hasMicrophone ? "✅ Available" : "❌ Not Available") + "\n";
        
        // Check speech recognition
        boolean speechAvailable = SpeechRecognizer.isRecognitionAvailable(this);
        deviceInfo += "🗣️ Speech Recognition: " + (speechAvailable ? "✅ Available" : "❌ Not Available") + "\n";
        
        // Check audio manager state
        if (audioManager != null) {
            boolean micMuted = audioManager.isMicrophoneMute();
            deviceInfo += "🔇 Microphone Muted: " + (micMuted ? "❌ Yes" : "✅ No") + "\n";
        }
        
        updateStatus(deviceInfo);
        Log.d(TAG, deviceInfo);
    }
    
    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
            != PackageManager.PERMISSION_GRANTED) {
            
            updateStatus("🔒 Requesting microphone permission...");
            ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.RECORD_AUDIO}, 
                MICROPHONE_PERMISSION_REQUEST);
        } else {
            onPermissionGranted();
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == MICROPHONE_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                updateStatus("✅ Microphone permission granted!");
                onPermissionGranted();
            } else {
                updateStatus("❌ Microphone permission denied. Cannot test microphone.");
                startButton.setEnabled(false);
            }
        }
    }
    
    private void onPermissionGranted() {
        try {
            // Initialize speech recognizer
            if (SpeechRecognizer.isRecognitionAvailable(this)) {
                speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
                speechRecognizer.setRecognitionListener(this);
                
                // Create recognizer intent with multiple language fallbacks
                recognizerIntent = createRecognizerIntentWithLanguageFallback();
                
                updateStatus("✅ Speech recognizer initialized. Ready to test!");
                startButton.setEnabled(true);

            } else {
                updateStatus("❌ Speech recognition not available on this device");
                startButton.setEnabled(false);
            }

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error setting up speech recognizer", e);
            updateStatus("❌ Error setting up speech recognizer: " + e.getMessage());
            startButton.setEnabled(false);
        }
    }

    /**
     * Create recognizer intent with language fallback for Android 15
     */
    private Intent createRecognizerIntentWithLanguageFallback() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        intent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 3);
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, getPackageName());

        // Try multiple language options for Samsung tablets
        String[] languageOptions = {
            Locale.getDefault().toString(),  // Device default
            "en-US",                         // US English
            "en-GB",                         // UK English
            "en",                            // Generic English
            "en-IN"                          // Indian English (common on Samsung)
        };

        // Use the first available language
        String selectedLanguage = "en-US"; // Default fallback
        for (String lang : languageOptions) {
            try {
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, lang);
                intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, lang);
                selectedLanguage = lang;
                break;
            } catch (Exception e) {
                android.util.Log.w(TAG, "Language " + lang + " not supported, trying next");
            }
        }

        android.util.Log.d(TAG, "🌐 Using language: " + selectedLanguage + " for speech recognition");
        addTranscription("🌐 Language set to: " + selectedLanguage);

        // Android 15 specific settings for Samsung tablets
        if (android.os.Build.VERSION.SDK_INT >= 35) {
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 5000);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 5000);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS, 1000);
            android.util.Log.d(TAG, "🤖 Applied Android 15 specific settings");
        }

        // Samsung specific settings
        String manufacturer = android.os.Build.MANUFACTURER.toLowerCase();
        if (manufacturer.contains("samsung")) {
            intent.putExtra("android.speech.extra.DICTATION_MODE", true);
            intent.putExtra("android.speech.extra.PARTIAL_RESULTS", true);
            android.util.Log.d(TAG, "📱 Applied Samsung tablet specific settings");
        }

        return intent;
    }
    
    private void startListening() {
        try {
            if (speechRecognizer != null && !isListening) {
                // Unmute microphone if needed
                if (audioManager != null && audioManager.isMicrophoneMute()) {
                    audioManager.setMicrophoneMute(false);
                    addTranscription("🔊 Microphone unmuted");
                }
                
                isListening = true;
                speechRecognizer.startListening(recognizerIntent);
                
                updateStatus("🎤 Listening... Speak now!");
                startButton.setEnabled(false);
                stopButton.setEnabled(true);
                
                addTranscription("🎤 Started listening at " + getCurrentTime());
                
                Log.d(TAG, "Started listening for speech");
                
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting listening", e);
            updateStatus("❌ Error starting listening: " + e.getMessage());
            addTranscription("❌ Error: " + e.getMessage());
            resetUI();
        }
    }
    
    private void stopListening() {
        try {
            if (speechRecognizer != null && isListening) {
                isListening = false;
                speechRecognizer.stopListening();
                
                updateStatus("⏹️ Stopped listening");
                addTranscription("⏹️ Stopped listening at " + getCurrentTime());
                
                Log.d(TAG, "Stopped listening");
            }
            resetUI();
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping listening", e);
            updateStatus("❌ Error stopping listening: " + e.getMessage());
            resetUI();
        }
    }
    
    private void clearTranscription() {
        transcriptionText.setText("");
        updateStatus("🧹 Transcription cleared. Ready to test!");
    }
    
    private void resetUI() {
        startButton.setEnabled(true);
        stopButton.setEnabled(false);
    }
    
    private void updateStatus(String status) {
        if (statusText != null) {
            statusText.setText(status);
        }
        Log.d(TAG, "Status: " + status);
    }
    
    private void addTranscription(String text) {
        if (transcriptionText != null) {
            String currentText = transcriptionText.getText().toString();
            String newText = currentText + "\n" + text;
            transcriptionText.setText(newText);
            
            // Auto-scroll to bottom
            if (transcriptionScroll != null) {
                transcriptionScroll.post(() -> transcriptionScroll.fullScroll(View.FOCUS_DOWN));
            }
        }
        Log.d(TAG, "Transcription: " + text);
    }
    
    private String getCurrentTime() {
        return new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date());
    }
    
    // RecognitionListener implementation
    @Override
    public void onReadyForSpeech(Bundle params) {
        Log.d(TAG, "Ready for speech");
        updateStatus("🎤 Ready for speech - microphone is active!");
        addTranscription("✅ Microphone activated successfully");
    }
    
    @Override
    public void onBeginningOfSpeech() {
        Log.d(TAG, "Beginning of speech detected");
        updateStatus("🗣️ Speech detected!");
        addTranscription("🗣️ Speech detected at " + getCurrentTime());
    }
    
    @Override
    public void onRmsChanged(float rmsdB) {
        // Audio level indicator - could add visual feedback here
    }
    
    @Override
    public void onBufferReceived(byte[] buffer) {
        Log.d(TAG, "Audio buffer received");
    }
    
    @Override
    public void onEndOfSpeech() {
        Log.d(TAG, "End of speech");
        updateStatus("⏸️ Processing speech...");
    }
    
    @Override
    public void onError(int error) {
        String errorMessage = getErrorMessage(error);
        android.util.Log.e(TAG, "Speech recognition error: " + error + " - " + errorMessage);

        updateStatus("❌ Error: " + errorMessage);
        addTranscription("❌ Error (" + error + "): " + errorMessage + " at " + getCurrentTime());

        isListening = false;
        resetUI();

        // Handle specific Android 15 Samsung tablet errors
        if (error == 11 || error == 12) { // Language errors
            addTranscription("🔧 Attempting language fallback for Samsung tablet...");
            handler.postDelayed(() -> {
                if (!isListening) {
                    // Try with different language settings
                    recreateRecognizerWithFallback();
                    startListening();
                }
            }, 2000);
        } else if (error != 9) { // Not permission error
            handler.postDelayed(() -> {
                if (!isListening) {
                    addTranscription("🔄 Auto-restarting in 2 seconds...");
                    handler.postDelayed(this::startListening, 2000);
                }
            }, 1000);
        }
    }

    /**
     * Recreate speech recognizer with different language fallback
     */
    private void recreateRecognizerWithFallback() {
        try {
            if (speechRecognizer != null) {
                speechRecognizer.destroy();
            }

            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            speechRecognizer.setRecognitionListener(this);

            // Create new intent with simplified settings for Samsung tablets
            recognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);

            // Use device default language only
            String deviceLanguage = Locale.getDefault().toString();
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, deviceLanguage);

            addTranscription("🌐 Recreated with device language: " + deviceLanguage);
            android.util.Log.d(TAG, "Recreated recognizer with device language: " + deviceLanguage);

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error recreating recognizer", e);
            addTranscription("❌ Failed to recreate recognizer: " + e.getMessage());
        }
    }
    
    @Override
    public void onResults(Bundle results) {
        ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
        if (matches != null && !matches.isEmpty()) {
            String recognizedText = matches.get(0);
            Log.d(TAG, "Recognized: " + recognizedText);
            
            addTranscription("📝 [" + getCurrentTime() + "] YOU SAID: \"" + recognizedText + "\"");
            updateStatus("✅ Speech recognized successfully!");
        }
        
        isListening = false;
        resetUI();
        
        // Auto-restart listening for continuous transcription
        handler.postDelayed(() -> {
            if (!isListening) {
                startListening();
            }
        }, 1000);
    }
    
    @Override
    public void onPartialResults(Bundle partialResults) {
        ArrayList<String> partialMatches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
        if (partialMatches != null && !partialMatches.isEmpty()) {
            String partialText = partialMatches.get(0);
            Log.d(TAG, "Partial: " + partialText);
            
            updateStatus("🎤 Hearing: \"" + partialText + "\"");
        }
    }
    
    @Override
    public void onEvent(int eventType, Bundle params) {
        Log.d(TAG, "Speech event: " + eventType);
    }
    
    private String getErrorMessage(int error) {
        switch (error) {
            case 3: // ERROR_AUDIO
                return "Audio recording error - microphone issue";
            case 5: // ERROR_CLIENT
                return "Client side error";
            case 9: // ERROR_INSUFFICIENT_PERMISSIONS
                return "Insufficient permissions";
            case 2: // ERROR_NETWORK
                return "Network error";
            case 1: // ERROR_NETWORK_TIMEOUT
                return "Network timeout";
            case 7: // ERROR_NO_MATCH
                return "No speech match found";
            case 8: // ERROR_RECOGNIZER_BUSY
                return "Recognition service busy";
            case 4: // ERROR_SERVER
                return "Server error";
            case 6: // ERROR_SPEECH_TIMEOUT
                return "No speech input";
            case 11: // ERROR_LANGUAGE_NOT_SUPPORTED (Android 15 Samsung issue)
                return "Language not supported - trying fallback language";
            case 12: // ERROR_LANGUAGE_UNAVAILABLE
                return "Language unavailable - switching to default language";
            default:
                return "Unknown error: " + error + " (Android 15 compatibility issue)";
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        
        Log.d(TAG, "MicrophoneTestActivity destroyed");
    }
}
