package com.stemrobo.humanoid;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.util.Log;
// Menu imports removed - using bottom navigation only
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.fragments.ControlFragment;
import com.stemrobo.humanoid.fragments.WorkingChatFragment;
import com.stemrobo.humanoid.fragments.SettingsFragment;
import com.stemrobo.humanoid.fragments.VisionFragment;
import com.stemrobo.humanoid.fragments.PresetFragment;
import com.stemrobo.humanoid.services.VoiceRecognitionService;
import com.stemrobo.humanoid.services.HeartRateSensorService;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import com.stemrobo.humanoid.services.HeartRateSensorService;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;

import java.util.Map;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    private ESP32CommunicationManager communicationManager;
    private TextView heartRateDisplay;
    private TextView connectionModeStatus;
    private TextView motorStatus;
    private TextView servoStatus;
    private TextView sensorStatus;
    private TextView voiceStatus;
    private BottomNavigationView bottomNavigation;

    // Smart Greeting status displays
    private TextView faceCountDisplay;
    private TextView distanceDisplay;
    private androidx.camera.view.PreviewView miniCameraPreview;

    // Continuous monitoring for Smart Greeting
    private Handler smartGreetingHandler;
    private Runnable distanceMonitoringRunnable;
    private static final int DISTANCE_UPDATE_INTERVAL = 200; // 200ms for real-time updates
    private boolean isSmartGreetingMonitoringActive = false;

    // Current status values
    private int currentFaceCount = 0;
    private float currentDistance = 999.0f;

    // Smart Greeting Service Manager
    private com.stemrobo.humanoid.services.SmartGreetingServiceManager smartGreetingServiceManager;

    // Screen saver components
    private FrameLayout screenSaverOverlay;
    private VideoView screenSaverVideo;
    private boolean isScreenSaverActive = false;

    // Heart rate sensor broadcast receiver
    private BroadcastReceiver heartRateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (HeartRateSensorService.ACTION_HEART_RATE_UPDATE.equals(action)) {
                int heartRate = intent.getIntExtra(HeartRateSensorService.EXTRA_HEART_RATE, 0);
                updateHeartRateDisplay(heartRate);
            } else if (HeartRateSensorService.ACTION_SENSOR_TOUCH_STATE.equals(action)) {
                boolean isTouched = intent.getBooleanExtra(HeartRateSensorService.EXTRA_IS_TOUCHED, false);
                updateHeartRateVisibility(isTouched);
            }
        }
    };

    // Screen saver broadcast receiver
    private BroadcastReceiver screenSaverReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "Screen saver broadcast received: " + action);
            if ("com.stemrobo.humanoid.ACTION_SHOW_SCREEN_SAVER".equals(action)) {
                Log.d(TAG, "Showing screen saver...");
                showScreenSaver();
            } else if ("com.stemrobo.humanoid.ACTION_HIDE_SCREEN_SAVER".equals(action)) {
                Log.d(TAG, "Hiding screen saver...");
                hideScreenSaver();
            }
        }
    };

    // Required permissions
    private final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_WIFI_STATE,
        Manifest.permission.CHANGE_WIFI_STATE,
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        Log.d(TAG, "MainActivity created");
        
        initializeViews();
        checkPermissions();
        initializeSystems();
        setupNavigation();
    }
    
    private void initializeViews() {
        heartRateDisplay = findViewById(R.id.heart_rate_display);
        connectionModeStatus = findViewById(R.id.connection_mode_status);
        motorStatus = findViewById(R.id.motor_status);
        servoStatus = findViewById(R.id.servo_status);
        sensorStatus = findViewById(R.id.sensor_status);
        voiceStatus = findViewById(R.id.voice_status);
        bottomNavigation = findViewById(R.id.bottom_navigation);

        // Initialize Smart Greeting status displays
        faceCountDisplay = findViewById(R.id.face_count_display);
        distanceDisplay = findViewById(R.id.distance_display);
        miniCameraPreview = findViewById(R.id.mini_camera_preview);

        // Debug: Check if views were found
        System.out.println("MainActivity: faceCountDisplay initialization: " + (faceCountDisplay == null ? "NULL" : "SUCCESS"));
        System.out.println("MainActivity: distanceDisplay initialization: " + (distanceDisplay == null ? "NULL" : "SUCCESS"));
        System.out.println("MainActivity: miniCameraPreview initialization: " + (miniCameraPreview == null ? "NULL" : "SUCCESS"));

        // Force initialize face count display if null
        if (faceCountDisplay == null) {
            System.err.println("MainActivity: faceCountDisplay is NULL! Trying alternative approach...");
            // Try to find it after a delay
            new android.os.Handler().postDelayed(() -> {
                faceCountDisplay = findViewById(R.id.face_count_display);
                System.out.println("MainActivity: Delayed faceCountDisplay initialization: " + (faceCountDisplay == null ? "STILL NULL" : "SUCCESS"));
                if (faceCountDisplay != null) {
                    faceCountDisplay.setText("Faces: 0");
                    faceCountDisplay.setVisibility(android.view.View.VISIBLE);
                }
            }, 1000);
        }

        // Initialize screen saver components
        screenSaverOverlay = findViewById(R.id.screen_saver_overlay);
        screenSaverVideo = findViewById(R.id.screen_saver_video);

        // Keep screen on for robot operation
        getWindow().addFlags(android.view.WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // Validate critical views
        if (bottomNavigation == null) {
            Log.e(TAG, "Bottom navigation view not found!");
            Toast.makeText(this, "UI initialization failed", Toast.LENGTH_LONG).show();
            finish();
        }

        // Initialize connection status display
        updateConnectionStatusDisplay();

        // Enable Smart Greeting status displays by default
        enableSmartGreetingStatusDisplay();
    }
    
    private void checkPermissions() {
        boolean allPermissionsGranted = true;
        
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) 
                != PackageManager.PERMISSION_GRANTED) {
                allPermissionsGranted = false;
                break;
            }
        }
        
        if (!allPermissionsGranted) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, PERMISSION_REQUEST_CODE);
        } else {
            onPermissionsGranted();
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            boolean microphoneGranted = false;

            // Check each permission result
            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    Log.w(TAG, "Permission denied: " + permissions[i]);
                } else {
                    if (Manifest.permission.RECORD_AUDIO.equals(permissions[i])) {
                        microphoneGranted = true;
                        Log.d(TAG, "Microphone permission granted");
                    }
                }
            }

            if (allGranted) {
                onPermissionsGranted();
            } else {
                // Handle specific permission failures
                if (!microphoneGranted) {
                    handleMicrophonePermissionDenied();
                } else {
                    Toast.makeText(this, "Some permissions required for robot operation were denied",
                                 Toast.LENGTH_LONG).show();
                    finish();
                }
            }
        }
    }

    /**
     * Handle microphone permission denial with Samsung tablet specific guidance
     */
    private void handleMicrophonePermissionDenied() {
        String manufacturer = Build.MANUFACTURER.toLowerCase();
        String message;

        if (manufacturer.contains("samsung")) {
            message = "Microphone permission is required for AI chat functionality.\n\n" +
                     "For Samsung tablets:\n" +
                     "1. Go to Settings > Apps > STEM Robot\n" +
                     "2. Tap Permissions\n" +
                     "3. Enable Microphone permission\n" +
                     "4. Restart the app";
        } else {
            message = "Microphone permission is required for AI chat functionality.\n" +
                     "Please enable microphone permission in app settings.";
        }

        new AlertDialog.Builder(this)
            .setTitle("Microphone Permission Required")
            .setMessage(message)
            .setPositiveButton("Open Settings", (dialog, which) -> {
                // Open app settings
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                Uri uri = Uri.fromParts("package", getPackageName(), null);
                intent.setData(uri);
                startActivity(intent);
            })
            .setNegativeButton("Continue Without Voice", (dialog, which) -> {
                // Continue without voice functionality
                onPermissionsGranted();
            })
            .setCancelable(false)
            .show();
    }
    
    private void onPermissionsGranted() {
        Log.d(TAG, "All permissions granted");
        registerMicrophoneErrorReceiver();
        startServices();
    }

    /**
     * Register receiver for microphone permission errors from VoiceRecognitionService
     */
    private void registerMicrophoneErrorReceiver() {
        IntentFilter filter = new IntentFilter("com.stemrobo.humanoid.MICROPHONE_PERMISSION_ERROR");
        LocalBroadcastManager.getInstance(this).registerReceiver(microphoneErrorReceiver, filter);
        Log.d(TAG, "Microphone error receiver registered");
    }

    /**
     * Broadcast receiver for microphone permission errors
     */
    private final BroadcastReceiver microphoneErrorReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.stemrobo.humanoid.MICROPHONE_PERMISSION_ERROR".equals(intent.getAction())) {
                runOnUiThread(() -> {
                    showMicrophoneErrorDialog();
                });
            }
        }
    };

    /**
     * Show dialog for microphone permission error with Samsung tablet guidance
     */
    private void showMicrophoneErrorDialog() {
        String manufacturer = Build.MANUFACTURER.toLowerCase();
        String message;

        if (manufacturer.contains("samsung")) {
            message = "Microphone access failed. This is common on Samsung tablets after system updates.\n\n" +
                     "To fix this:\n" +
                     "1. Go to Settings > Apps > STEM Robot\n" +
                     "2. Tap Permissions\n" +
                     "3. Turn Microphone OFF, then ON again\n" +
                     "4. Restart the app\n\n" +
                     "If the issue persists, try restarting your tablet.";
        } else {
            message = "Microphone access failed. Please check your device's microphone permissions.";
        }

        new AlertDialog.Builder(this)
            .setTitle("Microphone Access Issue")
            .setMessage(message)
            .setPositiveButton("Open Settings", (dialog, which) -> {
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                Uri uri = Uri.fromParts("package", getPackageName(), null);
                intent.setData(uri);
                startActivity(intent);
            })
            .setNegativeButton("OK", null)
            .show();
    }
    
    private void initializeSystems() {
        try {
            // Initialize communication manager
            communicationManager = ESP32CommunicationManager.getInstance();
            communicationManager.initialize(this);
            System.out.println(TAG + ": ESP32 Communication Manager initialized successfully");

            // Initialize Smart Greeting Service Manager
            smartGreetingServiceManager = com.stemrobo.humanoid.services.SmartGreetingServiceManager.getInstance();
            smartGreetingServiceManager.initialize(this);
            smartGreetingServiceManager.setCallback(new com.stemrobo.humanoid.services.SmartGreetingServiceManager.SmartGreetingCallback() {
                @Override
                public void onFaceCountUpdated(int faceCount) {
                    updateFaceCountDisplay(faceCount, true);
                }

                @Override
                public void onDistanceUpdated(float distance) {
                    updateDistanceDisplay(distance, true);
                }

                @Override
                public void onGreetingTriggered(String greetingType) {
                    System.out.println(TAG + ": Smart Greeting triggered: " + greetingType);
                    // TODO: Add UI feedback for greeting
                }

                @Override
                public void onSmartGreetingStatusChanged(boolean isActive) {
                    System.out.println(TAG + ": Smart Greeting status changed: " + isActive);
                }
            });
            System.out.println(TAG + ": Smart Greeting Service Manager initialized successfully");

            // CRITICAL: Ensure SmartGreetingManager is initialized and remains the primary listener
            ensureSmartGreetingManagerActive();

            // Start Smart Greeting services (this will start always-on camera)
            smartGreetingServiceManager.startSmartGreeting();

            // Make status indicators visible for Smart Greeting
            enableSmartGreetingStatusDisplay();

            // Initialize heart rate sensor monitoring
            initializeHeartRateSensor();

            // Start always-on camera immediately if enabled in settings
            checkAndStartBackgroundCamera();

        } catch (Exception e) {
            System.err.println(TAG + ": Error initializing systems: " + e.getMessage());
            // Don't finish the app, just log the error and continue
        }
    }

    private void initializeHeartRateSensor() {
        // Set up heart rate sensor monitoring
        // Heart rate display will only be visible when sensor is touched
        if (heartRateDisplay != null) {
            heartRateDisplay.setVisibility(android.view.View.GONE);
        }

        // TODO: Set up ESP32 heart rate sensor data listener
        // This will be implemented when ESP32 communication is ready

        // Register heart rate sensor broadcast receiver
        IntentFilter filter = new IntentFilter();
        filter.addAction(HeartRateSensorService.ACTION_HEART_RATE_UPDATE);
        filter.addAction(HeartRateSensorService.ACTION_SENSOR_TOUCH_STATE);
        LocalBroadcastManager.getInstance(this).registerReceiver(heartRateReceiver, filter);

        // Register screen saver broadcast receiver
        IntentFilter screenSaverFilter = new IntentFilter();
        screenSaverFilter.addAction("com.stemrobo.humanoid.ACTION_SHOW_SCREEN_SAVER");
        screenSaverFilter.addAction("com.stemrobo.humanoid.ACTION_HIDE_SCREEN_SAVER");
        LocalBroadcastManager.getInstance(this).registerReceiver(screenSaverReceiver, screenSaverFilter);
    }

    private void updateHeartRateDisplay(int heartRate) {
        if (heartRateDisplay != null) {
            heartRateDisplay.setText("♥ " + heartRate + " BPM");
        }
    }

    private void updateHeartRateVisibility(boolean isVisible) {
        if (heartRateDisplay != null) {
            heartRateDisplay.setVisibility(isVisible ? android.view.View.VISIBLE : android.view.View.GONE);
        }
    }

    private void startServices() {
        // Start voice recognition service
        Intent voiceIntent = new Intent(this, VoiceRecognitionService.class);
        startService(voiceIntent);

        // Start heart rate sensor service
        Intent heartRateIntent = new Intent(this, HeartRateSensorService.class);
        startService(heartRateIntent);

        // Start always-on camera service for face detection
        startAlwaysOnCameraService();

        // Initialize always-on face detection integration
        initializeAlwaysOnFaceDetectionIntegration();

        Log.d(TAG, "Services started");
    }

    /**
     * Start always-on camera service for continuous face detection
     */
    private void startAlwaysOnCameraService() {
        try {
            // Start background camera service
            Intent cameraIntent = new Intent(this, com.stemrobo.humanoid.services.BackgroundCameraService.class);
            cameraIntent.putExtra("action", "START_FACE_DETECTION");
            startService(cameraIntent);

            // Initialize face detection manager for always-on operation
            initializeAlwaysOnFaceDetection();

            Log.d(TAG, "Always-on camera service started");
        } catch (Exception e) {
            Log.e(TAG, "Error starting always-on camera service: " + e.getMessage(), e);
        }
    }

    /**
     * Initialize always-on face detection
     */
    private void initializeAlwaysOnFaceDetection() {
        try {
            // Create a face detection manager for background operation
            com.stemrobo.humanoid.vision.FaceDetectionManager faceManager =
                new com.stemrobo.humanoid.vision.FaceDetectionManager(this, this);

            // Set it as the singleton instance
            com.stemrobo.humanoid.vision.FaceDetectionManager.setInstance(faceManager);

            // Set callback to update status bar
            faceManager.setCallback(new com.stemrobo.humanoid.vision.FaceDetectionManager.FaceDetectionCallback() {
                @Override
                public void onFacesDetected(java.util.List<com.google.mlkit.vision.face.Face> faces, android.graphics.Bitmap cameraBitmap) {
                    int faceCount = faces.size();
                    updateFaceCountDisplay(faceCount, true);
                    Log.d(TAG, "Always-on face detection: " + faceCount + " faces detected");
                }

                @Override
                public void onNoFacesDetected() {
                    updateFaceCountDisplay(0, true);
                    Log.d(TAG, "Always-on face detection: No faces detected");
                }

                @Override
                public void onDetectionError(Exception error) {
                    Log.e(TAG, "Always-on face detection error: " + error.getMessage());
                }

                @Override
                public void onCameraFrame(android.graphics.Bitmap cameraBitmap) {
                    // Update mini camera preview if needed
                    updateMiniCameraPreview(cameraBitmap);
                }
            });

            // Start camera with face detection enabled
            startAlwaysOnCamera(faceManager);

        } catch (Exception e) {
            Log.e(TAG, "Error initializing always-on face detection: " + e.getMessage(), e);
        }
    }

    /**
     * Initialize always-on face detection integration with VisionFragment
     */
    private void initializeAlwaysOnFaceDetectionIntegration() {
        try {
            // Set up a timer to periodically check for face detection updates
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

            // Create a runnable that checks for face detection updates
            Runnable faceDetectionChecker = new Runnable() {
                @Override
                public void run() {
                    try {
                        // Get the singleton face detection manager instance
                        com.stemrobo.humanoid.vision.FaceDetectionManager faceManager =
                            com.stemrobo.humanoid.vision.FaceDetectionManager.getInstance();

                        if (faceManager != null && faceManager.isDetectionEnabled()) {
                            // Get current face count from the manager
                            int currentFaces = faceManager.getCurrentFaceCount();

                            // Update status bar display
                            updateFaceCountDisplay(currentFaces, true);

                            // Log for debugging
                            System.out.println("MainActivity: Face detection check - " + currentFaces + " faces");
                        }

                        // Schedule next check
                        mainHandler.postDelayed(this, 1000); // Check every 1 second

                    } catch (Exception e) {
                        System.err.println("MainActivity: Error in face detection checker: " + e.getMessage());
                        // Continue checking despite errors
                        mainHandler.postDelayed(this, 1000);
                    }
                }
            };

            // Start the periodic checker
            mainHandler.post(faceDetectionChecker);

            System.out.println("MainActivity: Always-on face detection integration initialized");

        } catch (Exception e) {
            System.err.println("MainActivity: Error initializing face detection integration: " + e.getMessage());
        }
    }

    /**
     * Start always-on camera for background face detection
     */
    private void startAlwaysOnCamera(com.stemrobo.humanoid.vision.FaceDetectionManager faceManager) {
        try {
            // Get camera provider
            androidx.camera.lifecycle.ProcessCameraProvider.getInstance(this)
                .addListener(() -> {
                    try {
                        androidx.camera.lifecycle.ProcessCameraProvider cameraProvider =
                            androidx.camera.lifecycle.ProcessCameraProvider.getInstance(this).get();

                        // Enable face detection
                        faceManager.setDetectionEnabled(true);

                        // Start camera with face detection
                        faceManager.startCamera(cameraProvider);

                        Log.d(TAG, "Always-on camera started successfully");

                    } catch (Exception e) {
                        Log.e(TAG, "Error getting camera provider: " + e.getMessage(), e);
                    }
                }, androidx.core.content.ContextCompat.getMainExecutor(this));

        } catch (Exception e) {
            System.err.println("MainActivity: Error starting always-on camera: " + e.getMessage());
        }
    }

    /**
     * Check background camera setting and start camera if enabled
     */
    private void checkAndStartBackgroundCamera() {
        try {
            // Check if background camera is enabled in settings
            android.content.SharedPreferences prefs = android.preference.PreferenceManager.getDefaultSharedPreferences(this);
            boolean backgroundCameraEnabled = com.stemrobo.humanoid.fragments.SettingsFragment.Settings.isBackgroundCameraEnabled(prefs);

            System.out.println("MainActivity: Background camera setting: " + backgroundCameraEnabled);

            if (backgroundCameraEnabled) {
                System.out.println("MainActivity: Starting always-on camera (enabled in settings)...");
                startAlwaysOnCameraImmediately();
                // Show camera preview and face count
                showCameraPreviewAndFaceCount();
            } else {
                System.out.println("MainActivity: Background camera disabled in settings - not starting camera");
                // Hide camera preview and face count
                hideCameraPreviewAndFaceCount();
            }

        } catch (Exception e) {
            System.err.println("MainActivity: Error checking background camera setting: " + e.getMessage());
            // Default to starting camera if there's an error reading settings
            startAlwaysOnCameraImmediately();
        }
    }

    /**
     * Start always-on camera immediately without waiting for permissions
     */
    private void startAlwaysOnCameraImmediately() {
        try {
            System.out.println("MainActivity: Starting always-on camera immediately...");

            // Enable status bar displays first
            enableSmartGreetingStatusDisplay();

            // Start the camera service
            startAlwaysOnCameraService();

            // Initialize face detection integration
            initializeAlwaysOnFaceDetectionIntegration();

            // Force start the camera with mini preview for background operation
            startCameraWithMiniPreview();

            System.out.println("MainActivity: Always-on camera started successfully");

        } catch (Exception e) {
            System.err.println("MainActivity: Error starting immediate camera: " + e.getMessage());
        }
    }

    /**
     * Start background camera for face detection without preview
     */
    private void startCameraWithMiniPreview() {
        try {
            System.out.println("MainActivity: Starting background face detection...");

            // Get camera provider and start background face detection
            androidx.camera.lifecycle.ProcessCameraProvider.getInstance(this)
                .addListener(() -> {
                    try {
                        androidx.camera.lifecycle.ProcessCameraProvider cameraProvider =
                            androidx.camera.lifecycle.ProcessCameraProvider.getInstance(this).get();

                        // Create background face detection manager (no preview needed)
                        com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager backgroundFaceManager =
                            new com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager(this, this);

                        // Set callback for face detection updates
                        backgroundFaceManager.setCallback(new com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager.FaceDetectionCallback() {
                            @Override
                            public void onFacesDetected(java.util.List<com.google.mlkit.vision.face.Face> faces, android.graphics.Bitmap bitmap) {
                                int faceCount = faces != null ? faces.size() : 0;
                                updateFaceCountDisplay(faceCount, true);
                                System.out.println("MainActivity: Background camera detected " + faceCount + " faces");

                                // Update mini camera preview if bitmap is available
                                if (bitmap != null) {
                                    updateMiniCameraPreview(bitmap);
                                }
                            }

                            @Override
                            public void onNoFacesDetected() {
                                updateFaceCountDisplay(0, true);
                                System.out.println("MainActivity: No faces detected");
                            }

                            @Override
                            public void onDetectionError(Exception error) {
                                System.err.println("MainActivity: Face detection error: " + error.getMessage());
                            }
                        });

                        // Enable face detection
                        backgroundFaceManager.setDetectionEnabled(true);

                        // Start camera with face detection (no preview required)
                        backgroundFaceManager.startCamera(cameraProvider);

                        // Set as singleton instance
                        com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager.setInstance(backgroundFaceManager);

                        System.out.println("MainActivity: Background face detection started successfully");

                    } catch (Exception e) {
                        System.err.println("MainActivity: Error starting background face detection: " + e.getMessage());
                    }
                }, androidx.core.content.ContextCompat.getMainExecutor(this));

        } catch (Exception e) {
            System.err.println("MainActivity: Error in startCameraWithMiniPreview: " + e.getMessage());
        }
    }

    /**
     * Show camera preview and face count display
     */
    private void showCameraPreviewAndFaceCount() {
        runOnUiThread(() -> {
            if (miniCameraPreview != null) {
                miniCameraPreview.setVisibility(android.view.View.VISIBLE);
            }
            if (faceCountDisplay != null) {
                faceCountDisplay.setVisibility(android.view.View.VISIBLE);
                faceCountDisplay.setText("Faces: 0");
            }
            System.out.println("MainActivity: Camera preview and face count display shown");
        });
    }

    /**
     * Hide camera preview and face count display
     */
    private void hideCameraPreviewAndFaceCount() {
        runOnUiThread(() -> {
            if (miniCameraPreview != null) {
                miniCameraPreview.setVisibility(android.view.View.GONE);
            }
            if (faceCountDisplay != null) {
                faceCountDisplay.setVisibility(android.view.View.GONE);
            }
            System.out.println("MainActivity: Camera preview and face count display hidden");
        });
    }

    /**
     * Update mini camera preview in status bar
     */
    private void updateMiniCameraPreview(android.graphics.Bitmap cameraBitmap) {
        if (miniCameraPreview != null && cameraBitmap != null) {
            runOnUiThread(() -> {
                try {
                    // Convert bitmap to drawable and set to preview
                    android.graphics.drawable.BitmapDrawable drawable =
                        new android.graphics.drawable.BitmapDrawable(getResources(), cameraBitmap);
                    miniCameraPreview.setBackground(drawable);
                } catch (Exception e) {
                    Log.e(TAG, "Error updating mini camera preview: " + e.getMessage());
                }
            });
        }
    }
    
    private void setupNavigation() {
        bottomNavigation.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;

            int itemId = item.getItemId();
            if (itemId == R.id.nav_control) {
                selectedFragment = new ControlFragment();
            } else if (itemId == R.id.nav_chat) {
                selectedFragment = new WorkingChatFragment();
            } else if (itemId == R.id.nav_vision) {
                selectedFragment = new VisionFragment();
            } else if (itemId == R.id.nav_preset) {
                selectedFragment = new PresetFragment();
            } else if (itemId == R.id.nav_settings) {
                selectedFragment = new SettingsFragment();
            }

            if (selectedFragment != null) {
                FragmentManager fragmentManager = getSupportFragmentManager();
                FragmentTransaction transaction = fragmentManager.beginTransaction();
                transaction.replace(R.id.fragment_container, selectedFragment);
                transaction.commit();

                // Ensure background camera stays active when switching tabs
                // This is important for Smart Greeting functionality
                System.out.println("MainActivity: Tab switched, ensuring background camera remains active");
                checkAndRestartBackgroundCameraIfNeeded();

                return true;
            }
            return false;
        });

        // Set default fragment
        bottomNavigation.setSelectedItemId(R.id.nav_control);
    }

    // Options menu removed - Settings moved to bottom navigation

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Unregister broadcast receivers
        try {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(heartRateReceiver);
            LocalBroadcastManager.getInstance(this).unregisterReceiver(screenSaverReceiver);
            LocalBroadcastManager.getInstance(this).unregisterReceiver(microphoneErrorReceiver);
        } catch (Exception e) {
            Log.w(TAG, "Error unregistering receivers", e);
        }

        if (communicationManager != null) {
            communicationManager.cleanup();
        }

        // Cleanup Smart Greeting Service Manager
        if (smartGreetingServiceManager != null) {
            smartGreetingServiceManager.cleanup();
        }

        Log.d(TAG, "MainActivity destroyed");
    }

    /**
     * Ensure SmartGreetingManager remains active as the primary communication listener
     */
    private void ensureSmartGreetingManagerActive() {
        try {
            // Get the singleton SmartGreetingManager instance
            com.stemrobo.humanoid.behaviors.SmartGreetingManager greetingManager =
                com.stemrobo.humanoid.behaviors.SmartGreetingManager.getInstance();

            if (greetingManager != null) {
                // Re-initialize to ensure it's the active listener
                greetingManager.initialize();

                // Load current settings
                android.content.SharedPreferences prefs = getSharedPreferences("com.stemrobo.humanoid_preferences", android.content.Context.MODE_PRIVATE);
                greetingManager.loadSettingsFromPreferences(prefs);

                System.out.println(TAG + ": SmartGreetingManager ensured as primary listener for cross-tab functionality");
            }
        } catch (Exception e) {
            System.out.println(TAG + ": Error ensuring SmartGreetingManager active: " + e.getMessage());
        }
    }
    
    private void updateConnectionStatusDisplay() {
        try {
            if (communicationManager == null) {
                Log.w(TAG, "Communication manager is null, skipping status update");
                return;
            }

            // Update connection mode status
            if (connectionModeStatus != null) {
                boolean usbConnected = communicationManager.isUsbConnected();
                if (usbConnected) {
                    connectionModeStatus.setText("USB: ●");
                    connectionModeStatus.setTextColor(getColor(R.color.status_connected));
                } else {
                    connectionModeStatus.setText("WiFi: ●");
                    connectionModeStatus.setTextColor(getColor(R.color.status_disconnected));
                }
            }

            // Update individual controller status
            Map<String, Boolean> connectionStatus = communicationManager.getAllConnectionStatus();
            Map<String, String> connectionModes = communicationManager.getAllConnectionModes();

            updateStatusIndicator(motorStatus, "Motor",
                                connectionStatus.getOrDefault("motor", false),
                                connectionModes.getOrDefault("motor", "wifi"));
            updateStatusIndicator(servoStatus, "Servo",
                                connectionStatus.getOrDefault("servo", false),
                                connectionModes.getOrDefault("servo", "wifi"));
            updateStatusIndicator(sensorStatus, "Sensor",
                                connectionStatus.getOrDefault("sensor", false),
                                connectionModes.getOrDefault("sensor", "wifi"));
        } catch (Exception e) {
            Log.e(TAG, "Error updating connection status display: " + e.getMessage(), e);
        }
    }

    private void updateStatusIndicator(TextView statusView, String name, boolean connected, String mode) {
        if (statusView != null) {
            String modeIcon = "usb".equals(mode) ? "🔌" : "📶";
            statusView.setText(name + ": " + modeIcon);
            statusView.setTextColor(getColor(connected ? R.color.status_connected : R.color.status_disconnected));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Update connection status when activity resumes
        updateConnectionStatusDisplay();

        // Check if background camera setting has changed and restart if needed
        checkAndRestartBackgroundCameraIfNeeded();

        // Handle orientation change for background camera
        handleOrientationChangeForBackgroundCamera();
    }

    /**
     * Handle orientation changes for background camera to ensure smooth preview
     */
    private void handleOrientationChangeForBackgroundCamera() {
        try {
            // Small delay to ensure orientation change is complete
            new android.os.Handler().postDelayed(() -> {
                try {
                    // Get background face manager instance
                    com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager backgroundManager =
                        com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager.getInstance();

                    if (backgroundManager != null) {
                        // Restart background camera with new orientation
                        System.out.println("MainActivity: Handling orientation change for background camera");
                        restartBackgroundCamera();
                    }
                } catch (Exception e) {
                    System.err.println("MainActivity: Error handling orientation change: " + e.getMessage());
                }
            }, 300);
        } catch (Exception e) {
            System.err.println("MainActivity: Error in handleOrientationChangeForBackgroundCamera: " + e.getMessage());
        }
    }

    /**
     * Check if background camera setting has changed and restart camera if needed
     */
    private void checkAndRestartBackgroundCameraIfNeeded() {
        try {
            android.content.SharedPreferences prefs = android.preference.PreferenceManager.getDefaultSharedPreferences(this);
            boolean backgroundCameraEnabled = com.stemrobo.humanoid.fragments.SettingsFragment.Settings.isBackgroundCameraEnabled(prefs);

            // Check if setting has changed
            if (backgroundCameraEnabled && !isCameraRunning()) {
                System.out.println("MainActivity: Background camera enabled - starting camera");
                startAlwaysOnCameraImmediately();
            } else if (!backgroundCameraEnabled && isCameraRunning()) {
                System.out.println("MainActivity: Background camera disabled - stopping camera");
                stopBackgroundCamera();
            }

        } catch (Exception e) {
            System.err.println("MainActivity: Error checking background camera setting: " + e.getMessage());
        }
    }

    private boolean isCameraRunning() {
        // Check if camera service is running
        try {
            return smartGreetingServiceManager != null && smartGreetingServiceManager.isSmartGreetingActive();
        } catch (Exception e) {
            return false;
        }
    }

    private void stopBackgroundCamera() {
        try {
            // Stop background face detection manager
            com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager backgroundManager =
                com.stemrobo.humanoid.vision.BackgroundFaceDetectionManager.getInstance();
            if (backgroundManager != null) {
                backgroundManager.stopCamera();
            }

            if (smartGreetingServiceManager != null) {
                smartGreetingServiceManager.stopSmartGreeting();
            }

            // Stop background camera service
            Intent cameraIntent = new Intent(this, com.stemrobo.humanoid.services.BackgroundCameraService.class);
            cameraIntent.putExtra("action", "STOP_FACE_DETECTION");
            startService(cameraIntent);

            // Hide face count display
            updateFaceCountDisplay(0, false);

            System.out.println("MainActivity: Background camera stopped");
        } catch (Exception e) {
            System.err.println("MainActivity: Error stopping background camera: " + e.getMessage());
        }
    }

    public ESP32CommunicationManager getCommunicationManager() {
        return communicationManager;
    }

    /**
     * Enable Smart Greeting status display indicators
     */
    private void enableSmartGreetingStatusDisplay() {
        try {
            if (faceCountDisplay != null) {
                faceCountDisplay.setVisibility(android.view.View.VISIBLE);
                faceCountDisplay.setText("Faces: 0");
                faceCountDisplay.setTextColor(0xFF757575); // Gray
            }

            if (distanceDisplay != null) {
                distanceDisplay.setVisibility(android.view.View.VISIBLE);
                distanceDisplay.setText("Distance: --.-cm");
                distanceDisplay.setTextColor(0xFF757575); // Gray
            }

            // Show mini camera preview for Smart Greeting
            if (miniCameraPreview != null) {
                miniCameraPreview.setVisibility(android.view.View.VISIBLE);
            }

            System.out.println("MainActivity: Smart Greeting status displays enabled");
        } catch (Exception e) {
            System.err.println("MainActivity: Error enabling status displays: " + e.getMessage());
        }
    }

    /**
     * Update face count display in the top status bar
     */
    public void updateFaceCountDisplay(int faceCount, boolean smartGreetingEnabled) {
        currentFaceCount = faceCount;

        System.out.println("MainActivity: updateFaceCountDisplay called - faceCount: " + faceCount + ", smartGreetingEnabled: " + smartGreetingEnabled);
        System.out.println("MainActivity: faceCountDisplay null check: " + (faceCountDisplay == null ? "NULL" : "NOT NULL"));

        runOnUiThread(() -> {
            try {
                System.out.println("MainActivity: Inside runOnUiThread block");
                if (faceCountDisplay != null) {
                    System.out.println("MainActivity: faceCountDisplay is not null, proceeding with update");
                    if (smartGreetingEnabled && faceCount >= 0) {
                        faceCountDisplay.setText("Faces: " + faceCount);
                        faceCountDisplay.setVisibility(android.view.View.VISIBLE);

                        System.out.println("MainActivity: Face count display updated - Text: 'Faces: " + faceCount + "', Visibility: VISIBLE");

                        // Color coding: Green if faces detected, gray if none
                        if (faceCount > 0) {
                            faceCountDisplay.setTextColor(0xFF4CAF50); // Green
                            System.out.println("MainActivity: Face count color set to GREEN");
                        } else {
                            faceCountDisplay.setTextColor(0xFF757575); // Gray
                            System.out.println("MainActivity: Face count color set to GRAY");
                        }
                    } else {
                        faceCountDisplay.setVisibility(android.view.View.GONE);
                        System.out.println("MainActivity: Face count display HIDDEN - smartGreetingEnabled: " + smartGreetingEnabled + ", faceCount: " + faceCount);
                    }
                } else {
                    System.err.println("MainActivity: faceCountDisplay is NULL in runOnUiThread!");
                }
            } catch (Exception e) {
                System.err.println("MainActivity: Exception in updateFaceCountDisplay: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Update distance display in the top status bar
     */
    public void updateDistanceDisplay(float distance, boolean smartGreetingEnabled) {
        currentDistance = distance;

        runOnUiThread(() -> {
            if (distanceDisplay != null) {
                if (smartGreetingEnabled) {
                    if (distance >= 999.0f) {
                        distanceDisplay.setText("Distance: --.-cm");
                        distanceDisplay.setTextColor(0xFF757575); // Gray
                    } else {
                        distanceDisplay.setText(String.format("Distance: %.1fcm", distance));

                        // Color coding: green if within greeting range (≤30cm), orange if close, gray if far
                        if (distance <= 30.0f) {
                            distanceDisplay.setTextColor(0xFF4CAF50); // Green - greeting range
                        } else if (distance <= 50.0f) {
                            distanceDisplay.setTextColor(0xFFFF9800); // Orange - close
                        } else {
                            distanceDisplay.setTextColor(0xFF757575); // Gray - far
                        }
                    }
                    distanceDisplay.setVisibility(View.VISIBLE);
                } else {
                    distanceDisplay.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * Show full-screen screen saver overlay
     */
    public void showScreenSaver() {
        try {
            Log.d(TAG, "showScreenSaver() called");
            if (screenSaverOverlay != null && !isScreenSaverActive) {
                Log.d(TAG, "Screen saver overlay found, activating...");
                isScreenSaverActive = true;

                // Setup video for full-screen display
                setupFullScreenVideo();

                // Enter immersive full-screen mode
                enterFullScreenMode();

                // Show overlay
                screenSaverOverlay.setVisibility(View.VISIBLE);

                // Set click listener to exit screen saver
                screenSaverOverlay.setOnClickListener(v -> {
                    Log.d(TAG, "Screen saver clicked, hiding...");
                    hideScreenSaver();
                });

                Log.d(TAG, "Screen saver activated successfully");
            } else {
                Log.w(TAG, "Screen saver not activated - overlay: " + (screenSaverOverlay != null) +
                          ", active: " + isScreenSaverActive);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing screen saver", e);
            // Try fallback screen saver without video
            showFallbackScreenSaver();
        }
    }

    /**
     * Fallback screen saver without video
     */
    private void showFallbackScreenSaver() {
        try {
            Log.d(TAG, "Showing fallback screen saver");
            if (screenSaverOverlay != null) {
                isScreenSaverActive = true;

                // Hide video view if it exists
                if (screenSaverVideo != null) {
                    screenSaverVideo.setVisibility(View.GONE);
                }

                // Set black background
                screenSaverOverlay.setBackgroundColor(0xFF000000);

                // Enter full-screen mode
                enterFullScreenMode();

                // Show overlay
                screenSaverOverlay.setVisibility(View.VISIBLE);

                // Set click listener to exit
                screenSaverOverlay.setOnClickListener(v -> hideScreenSaver());

                Log.d(TAG, "Fallback screen saver activated");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing fallback screen saver", e);
        }
    }

    /**
     * Hide screen saver overlay and restore normal UI
     */
    public void hideScreenSaver() {
        try {
            if (screenSaverOverlay != null && isScreenSaverActive) {
                isScreenSaverActive = false;

                // Hide overlay
                screenSaverOverlay.setVisibility(View.GONE);

                // Stop video playback
                if (screenSaverVideo != null && screenSaverVideo.isPlaying()) {
                    screenSaverVideo.stopPlayback();
                }

                // Exit full-screen mode
                exitFullScreenMode();

                Log.d(TAG, "Screen saver deactivated");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding screen saver", e);
        }
    }

    /**
     * Setup video for full-screen display with proper scaling
     */
    private void setupFullScreenVideo() {
        try {
            if (screenSaverVideo != null) {
                // Get video URI
                Uri videoUri = Uri.parse("android.resource://" +
                    getPackageName() + "/" + R.raw.screen);

                // Set video URI
                screenSaverVideo.setVideoURI(videoUri);

                // Configure video for full-screen display
                screenSaverVideo.setOnPreparedListener(mp -> {
                    try {
                        // Enable looping
                        mp.setLooping(true);

                        // Set video scaling to fill entire screen with cropping
                        mp.setVideoScalingMode(android.media.MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING);

                        // Get screen dimensions
                        DisplayMetrics displayMetrics = new DisplayMetrics();
                        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
                        int screenWidth = displayMetrics.widthPixels;
                        int screenHeight = displayMetrics.heightPixels;

                        // Force VideoView to fill entire screen
                        FrameLayout.LayoutParams layoutParams =
                            new FrameLayout.LayoutParams(screenWidth, screenHeight);
                        layoutParams.gravity = android.view.Gravity.CENTER;
                        screenSaverVideo.setLayoutParams(layoutParams);

                        // Start playback
                        screenSaverVideo.start();

                        Log.d(TAG, "Full-screen video setup completed - Screen: " + screenWidth + "x" + screenHeight);
                    } catch (Exception e) {
                        Log.e(TAG, "Error in video preparation", e);
                        // Fallback: just start the video normally
                        screenSaverVideo.start();
                    }
                });

                // Handle video errors
                screenSaverVideo.setOnErrorListener((mp, what, extra) -> {
                    Log.e(TAG, "Video playback error: " + what + ", " + extra);
                    return false; // Let VideoView handle the error
                });

                // Handle video completion (shouldn't happen with looping, but just in case)
                screenSaverVideo.setOnCompletionListener(mp -> {
                    Log.d(TAG, "Video completed, restarting...");
                    screenSaverVideo.start();
                });

                Log.d(TAG, "Video setup initiated for full-screen display");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up full-screen video", e);
        }
    }

    /**
     * Enter immersive full-screen mode
     */
    private void enterFullScreenMode() {
        try {
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;

            decorView.setSystemUiVisibility(uiOptions);

            // Hide action bar if present
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }

            Log.d(TAG, "Entered immersive full-screen mode");
        } catch (Exception e) {
            Log.e(TAG, "Error entering full-screen mode", e);
        }
    }

    /**
     * Exit immersive full-screen mode
     */
    private void exitFullScreenMode() {
        try {
            // Restore system UI - clear immersive flags
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);

            // Show action bar if present
            if (getSupportActionBar() != null) {
                getSupportActionBar().show();
            }

            Log.d(TAG, "Exited immersive full-screen mode");
        } catch (Exception e) {
            Log.e(TAG, "Error exiting full-screen mode", e);
        }
    }

    /**
     * Start continuous monitoring for Smart Greeting
     * This method should be called when Smart Greeting is enabled
     */
    public void startSmartGreetingMonitoring() {
        if (smartGreetingServiceManager != null) {
            smartGreetingServiceManager.startSmartGreeting();
            System.out.println(TAG + ": Smart Greeting background monitoring started via service manager");
        } else {
            // Fallback: Simple implementation - just update displays with current values
            updateFaceCountDisplay(currentFaceCount, true);
            updateDistanceDisplay(currentDistance, true);
            System.out.println(TAG + ": Smart Greeting monitoring started (fallback mode)");
        }
    }

    /**
     * Stop continuous monitoring for Smart Greeting
     */
    public void stopSmartGreetingMonitoring() {
        if (smartGreetingServiceManager != null) {
            smartGreetingServiceManager.stopSmartGreeting();
            System.out.println(TAG + ": Smart Greeting background monitoring stopped via service manager");
        } else {
            // Fallback: Hide displays when monitoring is stopped
            updateFaceCountDisplay(0, false);
            updateDistanceDisplay(999.0f, false);
            System.out.println(TAG + ": Smart Greeting monitoring stopped (fallback mode)");
        }
    }

    /**
     * Update both face count and distance displays
     * This method can be called from VisionFragment or other components
     */
    public void updateSmartGreetingStatus(int faceCount, float distance) {
        updateFaceCountDisplay(faceCount, true);
        updateDistanceDisplay(distance, true);
    }

    /**
     * Restart background camera system
     * Called when VisionFragment is destroyed to restore background face detection
     */
    public void restartBackgroundCamera() {
        try {
            System.out.println("MainActivity: Restarting background camera system...");

            // Stop any existing background camera
            stopBackgroundCamera();

            // Wait a moment for cleanup
            new android.os.Handler().postDelayed(() -> {
                try {
                    // Restart the background camera with mini preview
                    startCameraWithMiniPreview();
                    System.out.println("MainActivity: Background camera restarted successfully");
                } catch (Exception e) {
                    System.err.println("MainActivity: Error restarting background camera: " + e.getMessage());
                }
            }, 200);

        } catch (Exception e) {
            System.err.println("MainActivity: Error in restartBackgroundCamera: " + e.getMessage());
        }
    }
}
