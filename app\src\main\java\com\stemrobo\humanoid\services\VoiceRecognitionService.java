package com.stemrobo.humanoid.services;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stemrobo.humanoid.ai.ConversationMemoryManager;
import com.stemrobo.humanoid.ai.GeminiAIManager;
import com.stemrobo.humanoid.ai.GeminiAIService;
import com.stemrobo.humanoid.ai.SimpleRealTimeDataService;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.language.LanguageManager;
import com.stemrobo.humanoid.activities.LMSVideoPlayerActivity;
import com.stemrobo.humanoid.activities.LMSYouTubePlayerActivity;
import com.stemrobo.humanoid.fragments.SettingsFragment;
import com.stemrobo.humanoid.services.ResponsiveVoiceService;
import com.stemrobo.humanoid.database.PresetDatabase;
import com.stemrobo.humanoid.database.PresetDao;
import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.services.PresetExecutionService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

public class VoiceRecognitionService extends Service implements RecognitionListener, TextToSpeech.OnInitListener {
    private static final String TAG = "VoiceRecognitionService";
    private static final String WAKE_WORD = "hey robot";

    // Broadcast actions
    public static final String ACTION_VOICE_RESULT = "com.stemrobo.humanoid.VOICE_RESULT";
    public static final String ACTION_LISTENING_STATE = "com.stemrobo.humanoid.LISTENING_STATE";
    public static final String ACTION_PROCESS_TEXT_INPUT = "com.stemrobo.humanoid.PROCESS_TEXT_INPUT";
    public static final String ACTION_TRANSCRIPTION_UPDATE = "com.stemrobo.humanoid.TRANSCRIPTION_UPDATE";
    public static final String ACTION_START_PUSH_TO_TALK = "com.stemrobo.humanoid.START_PUSH_TO_TALK";
    public static final String ACTION_STOP_PUSH_TO_TALK = "com.stemrobo.humanoid.STOP_PUSH_TO_TALK";
    public static final String ACTION_MUTE_STATE_CHANGED = "com.stemrobo.humanoid.MUTE_STATE_CHANGED";
    public static final String ACTION_MUTE_SPEECH = "com.stemrobo.humanoid.MUTE_SPEECH";
    public static final String ACTION_UNMUTE_SPEECH = "com.stemrobo.humanoid.UNMUTE_SPEECH";

    // Intent extras
    public static final String EXTRA_VOICE_TEXT = "voice_text";
    public static final String EXTRA_AI_RESPONSE = "ai_response";
    public static final String EXTRA_IS_LISTENING = "is_listening";
    public static final String EXTRA_TEXT_INPUT = "text_input";
    public static final String EXTRA_PARTIAL_TEXT = "partial_text";

    // LMS broadcast actions
    public static final String ACTION_LMS_VIDEO_REQUEST = "com.stemrobo.humanoid.LMS_VIDEO_REQUEST";
    public static final String EXTRA_LMS_TYPE = "lms_type";
    public static final String EXTRA_CLASS_NUMBER = "class_number";

    // LMS types
    public static final String LMS_TYPE_INTRO = "intro";
    public static final String LMS_TYPE_CLASS = "class";

    private SpeechRecognizer speechRecognizer;
    private TextToSpeech textToSpeech;
    private Intent recognizerIntent;
    private Handler handler;
    private GeminiAIService geminiAI;
    private com.stemrobo.humanoid.ai.EnhancedGeminiAIService enhancedGeminiAI;
    private GeminiAIManager geminiAIManager;
    private com.stemrobo.humanoid.ai.ConversationMemoryManager conversationMemory;
    private ESP32CommunicationManager esp32Manager;
    private LanguageManager languageManager;
    private AudioManager audioManager;

    // Movement duration timers
    private Handler movementHandler;
    private Runnable stopMovementRunnable;

    // Audio feedback prevention
    private boolean isSpeaking = false;
    private static final String UTTERANCE_ID = "robot_speech";

    // ResponsiveVoice Service for enhanced male voice
    private ResponsiveVoiceService responsiveVoiceService;
    private boolean isResponsiveVoiceReady = false;
    private boolean isUsingResponsiveVoice = false;

    // State management
    private boolean isListening = false;
    private boolean isWakeWordMode = true;
    private boolean isInConversation = false;
    private boolean isPushToTalkMode = false;
    private boolean isPushToTalkActive = false; // Currently holding push-to-talk button
    private boolean wasListeningBeforePushToTalk = false; // To restore previous state
    private String pendingPushToTalkText = null; // Store speech until button release
    private long lastSpeechTime = 0;
    private static final long SILENCE_TIMEOUT = 4000; // 4 seconds (fallback)
    private SharedPreferences sharedPreferences;

    // External mute control
    private boolean isExternallyMuted = false;

    // Robot type configuration
    private String currentRobotType = "mecanum"; // Default to mecanum
    private BroadcastReceiver robotTypeReceiver;

    // Robot commands
    private final List<String> ROBOT_COMMANDS = Arrays.asList(
        "move forward", "go forward", "move backward", "go backward",
        "turn left", "turn right", "stop", "wave", "point", "rest",
        "center head", "look up", "look down", "turn off guruji"
    );
    
    // Broadcast receiver for language changes
    private final BroadcastReceiver languageChangeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("com.stemrobo.humanoid.LANGUAGE_CHANGED".equals(intent.getAction())) {
                String newLanguage = intent.getStringExtra("language_code");
                Log.d(TAG, "🌍 Language change received: " + newLanguage);

                // Update language manager
                if (languageManager != null && newLanguage != null) {
                    String previousLanguage = languageManager.getCurrentLanguage();
                    languageManager.setCurrentLanguage(newLanguage);

                    // Stop current listening to apply new language
                    boolean wasListening = isListening;
                    boolean wasInConversation = !isWakeWordMode;

                    if (wasListening) {
                        stopListening();
                    }

                    // Recreate speech recognizer with new language
                    recreateSpeechRecognizer();
                    updateTextToSpeechLanguage();

                    // Announce language change in new language
                    String languageName = languageManager.getCurrentLanguageName();
                    String wakeWord = languageManager.getWakeWord();
                    speak("Language changed to " + languageName + ". Wake word is " + wakeWord);

                    // Restart listening if it was active
                    if (wasListening) {
                        handler.postDelayed(() -> {
                            // Maintain conversation mode if we were in conversation
                            if (wasInConversation) {
                                isWakeWordMode = false;
                            }
                            startListening();
                        }, 2000);
                    }

                    Log.d(TAG, "✅ Language successfully changed from " + previousLanguage + " to " + newLanguage);
                    Log.d(TAG, "🎤 New wake word: " + wakeWord);
                    Log.d(TAG, "🗣️ Conversation mode: " + (wasInConversation ? "maintained" : "wake word mode"));
                }
            }
        }
    };

    // Broadcast receiver for push-to-talk actions
    private final BroadcastReceiver pushToTalkReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null || intent.getAction() == null) {
                    return;
                }

                String action = intent.getAction();
                if (ACTION_START_PUSH_TO_TALK.equals(action)) {
                    startPushToTalkListening();
                } else if (ACTION_STOP_PUSH_TO_TALK.equals(action)) {
                    stopPushToTalkListening();
                }
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error in push-to-talk receiver", e);
            }
        }
    };

    // Broadcast receiver for control commands (mute, clear conversation)
    private final BroadcastReceiver controlReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null || intent.getAction() == null) {
                    return;
                }

                String action = intent.getAction();
                if (ACTION_MUTE_SPEECH.equals(action)) {
                    setExternalMute(true);
                } else if (ACTION_UNMUTE_SPEECH.equals(action)) {
                    setExternalMute(false);
                } else if ("com.stemrobo.humanoid.CLEAR_CONVERSATION_MEMORY".equals(action)) {
                    clearConversationMemory();
                }
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error in control receiver", e);
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "VoiceRecognitionService created");

        // Initialize handler first
        handler = new Handler(Looper.getMainLooper());

        // Initialize shared preferences
        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(this);

        // Initialize robot type
        currentRobotType = sharedPreferences.getString("robot_type", "mecanum");
        setupRobotTypeReceiver();

        initializeSpeechRecognizer();
        initializeTextToSpeech();
        initializeResponsiveVoice();
        initializeAIService();
        initializeESP32Manager();
        initializeLanguageManager();
        registerLanguageChangeReceiver();
        registerPushToTalkReceiver();
        registerControlReceiver();
        registerVoiceSettingsReceiver();
        registerMicrophoneTestReceiver();
    }

    private void registerLanguageChangeReceiver() {
        IntentFilter filter = new IntentFilter("com.stemrobo.humanoid.LANGUAGE_CHANGED");
        LocalBroadcastManager.getInstance(this).registerReceiver(languageChangeReceiver, filter);
        Log.d(TAG, "Language change receiver registered");
    }

    private void registerPushToTalkReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_START_PUSH_TO_TALK);
        filter.addAction(ACTION_STOP_PUSH_TO_TALK);
        LocalBroadcastManager.getInstance(this).registerReceiver(pushToTalkReceiver, filter);
        Log.d(TAG, "Push-to-talk receiver registered");
    }

    private void registerControlReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_MUTE_SPEECH);
        filter.addAction(ACTION_UNMUTE_SPEECH);
        filter.addAction("com.stemrobo.humanoid.CLEAR_CONVERSATION_MEMORY");
        LocalBroadcastManager.getInstance(this).registerReceiver(controlReceiver, filter);
        Log.d(TAG, "Control receiver registered");
    }

    // Broadcast receiver for voice settings changes
    private final BroadcastReceiver voiceSettingsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if ("com.stemrobo.humanoid.VOICE_SETTINGS_CHANGED".equals(intent.getAction())) {
                    String voiceGender = intent.getStringExtra("voice_gender");
                    android.util.Log.d(TAG, "🎤 Voice gender setting changed: " + voiceGender);

                    // Reconfigure voice gender
                    configureVoiceGender();
                }
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error handling voice settings change: " + e.getMessage());
            }
        }
    };

    private void registerVoiceSettingsReceiver() {
        IntentFilter filter = new IntentFilter("com.stemrobo.humanoid.VOICE_SETTINGS_CHANGED");
        if (android.os.Build.VERSION.SDK_INT >= 33) { // API 33 = Android 13 (Tiramisu)
            registerReceiver(voiceSettingsReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(voiceSettingsReceiver, filter);
        }
        android.util.Log.d(TAG, "Voice settings receiver registered");
    }

    /**
     * Broadcast receiver for microphone test commands
     */
    private final BroadcastReceiver microphoneTestReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if ("com.stemrobo.humanoid.TEST_MICROPHONE".equals(intent.getAction())) {
                    android.util.Log.d(TAG, "🎤 Microphone test command received");
                    performMicrophoneTestFromService();
                }
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error in microphone test receiver", e);
            }
        }
    };

    private void registerMicrophoneTestReceiver() {
        IntentFilter filter = new IntentFilter("com.stemrobo.humanoid.TEST_MICROPHONE");
        if (android.os.Build.VERSION.SDK_INT >= 33) { // API 33 = Android 13 (Tiramisu)
            registerReceiver(microphoneTestReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(microphoneTestReceiver, filter);
        }
        android.util.Log.d(TAG, "Microphone test receiver registered");
    }

    /**
     * Perform microphone test from service
     */
    private void performMicrophoneTestFromService() {
        try {
            android.util.Log.d(TAG, "🎤 Performing microphone test from VoiceRecognitionService");

            // Reset audio manager settings
            resetAudioManagerSettings();

            // Recreate speech recognizer if needed
            if (speechRecognizer == null) {
                initializeSpeechRecognizer();
            }

            // Test speech recognition
            if (speechRecognizer != null && !isListening) {
                android.util.Log.d(TAG, "🎤 Testing speech recognition functionality");

                // Try to start listening briefly to test microphone
                try {
                    Intent recognizerIntent = createRecognizerIntentWithSamsungSupport();
                    speechRecognizer.startListening(recognizerIntent);

                    // Stop after a short test
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            if (speechRecognizer != null && isListening) {
                                speechRecognizer.stopListening();
                                android.util.Log.d(TAG, "🎤 Microphone test completed - speech recognition working");
                            }
                        }, 2000);
                    }
                } catch (Exception e) {
                    android.util.Log.e(TAG, "🎤 Microphone test failed - speech recognition error", e);
                }
            }

            // Speak test message
            speak("Microphone test completed. Voice system is working.");

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error performing microphone test from service", e);
        }
    }

    private void initializeAIService() {
        try {
            geminiAI = new GeminiAIService();
            enhancedGeminiAI = new com.stemrobo.humanoid.ai.EnhancedGeminiAIService(this);
            conversationMemory = new com.stemrobo.humanoid.ai.ConversationMemoryManager(this);

            // Initialize GeminiAIManager with robot action callback
            geminiAIManager = new GeminiAIManager(this);
            geminiAIManager.setRobotActionCallback(new GeminiAIManager.RobotActionCallback() {
                @Override
                public void executeMovement(String direction, int speed, int duration) {
                    executeMovementWithDuration(direction, duration);
                }

                @Override
                public void executeGesture(String gesture) {
                    sendServoCommand(gesture);
                }

                @Override
                public void executeHeadMovement(int pan, int tilt) {
                    // Head movement not implemented in ESP32 yet
                }

                @Override
                public void speak(String text) {
                    VoiceRecognitionService.this.speak(text);
                }
            });

            Log.d(TAG, "GeminiAI service, manager and conversation memory initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing AI services", e);
        }
    }

    private void initializeESP32Manager() {
        try {
            esp32Manager = ESP32CommunicationManager.getInstance();
            esp32Manager.initialize(this);
            Log.d(TAG, "ESP32 communication manager initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing ESP32 manager", e);
        }
    }

    private void initializeLanguageManager() {
        try {
            languageManager = new LanguageManager(this);
            android.util.Log.d(TAG, "Language manager initialized successfully");
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error initializing language manager", e);
        }
    }

    private void initializeSpeechRecognizer() {
        try {
            // Check if speech recognition is available
            if (!SpeechRecognizer.isRecognitionAvailable(this)) {
                android.util.Log.e(TAG, "🎤 Speech recognition not available on this device");
                return;
            }

            // Check microphone permission before initializing
            if (!checkMicrophonePermission()) {
                android.util.Log.e(TAG, "🎤 Microphone permission not granted");
                return;
            }

            // Initialize audio manager for Samsung tablet compatibility
            initializeAudioManagerForSamsung();

            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            if (speechRecognizer == null) {
                android.util.Log.e(TAG, "🎤 Failed to create speech recognizer");
                return;
            }

            speechRecognizer.setRecognitionListener(this);
            updateSpeechRecognizerLanguage();

            android.util.Log.d(TAG, "🎤 Speech recognizer initialized successfully with Samsung tablet support");

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error initializing speech recognizer", e);
        }
    }

    /**
     * Check if microphone permission is granted
     */
    private boolean checkMicrophonePermission() {
        return androidx.core.content.ContextCompat.checkSelfPermission(this,
            android.Manifest.permission.RECORD_AUDIO) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Initialize audio manager with Samsung tablet specific settings
     */
    private void initializeAudioManagerForSamsung() {
        try {
            if (audioManager != null) {
                // Ensure microphone is not muted
                audioManager.setMicrophoneMute(false);

                // Set audio mode to normal for better compatibility
                audioManager.setMode(AudioManager.MODE_NORMAL);

                // Check if this is a Samsung device and apply specific settings
                String manufacturer = android.os.Build.MANUFACTURER.toLowerCase();
                String model = android.os.Build.MODEL.toLowerCase();

                if (manufacturer.contains("samsung")) {
                    android.util.Log.d(TAG, "🎤 Samsung device detected: " + model + " - applying compatibility settings");

                    // Samsung-specific audio settings
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC,
                        audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC) / 2, 0);

                    // Ensure microphone is available
                    if (audioManager.isMicrophoneMute()) {
                        audioManager.setMicrophoneMute(false);
                        android.util.Log.d(TAG, "🎤 Samsung tablet microphone unmuted");
                    }
                }

                android.util.Log.d(TAG, "🎤 Audio manager initialized for device: " + manufacturer + " " + model);
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error initializing audio manager for Samsung compatibility", e);
        }
    }

    /**
     * Recreate speech recognizer with new language settings
     */
    private void recreateSpeechRecognizer() {
        try {
            // Destroy existing speech recognizer
            if (speechRecognizer != null) {
                speechRecognizer.destroy();
            }

            // Create new speech recognizer
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            speechRecognizer.setRecognitionListener(this);

            // Update with new language settings
            updateSpeechRecognizerLanguage();

            Log.d(TAG, "Speech recognizer recreated with new language settings");
        } catch (Exception e) {
            Log.e(TAG, "Error recreating speech recognizer", e);
        }
    }

    /**
     * Update speech recognizer language based on current language setting
     */
    private void updateSpeechRecognizerLanguage() {
        recognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);

        // Set language based on current language manager setting
        if (languageManager != null) {
            Locale speechLocale = languageManager.getSpeechLocale();
            if (speechLocale != null) {
                recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, speechLocale);
                recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, speechLocale.toString());
                Log.d(TAG, "Speech recognizer set to language: " + speechLocale.toString());
            } else {
                recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault());
            }
        } else {
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault());
        }
    }
    
    private void initializeTextToSpeech() {
        textToSpeech = new TextToSpeech(this, this);

        // Initialize audio manager for feedback prevention
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
    }

    /**
     * Initialize ResponsiveVoice service for enhanced male voice
     */
    private void initializeResponsiveVoice() {
        try {
            responsiveVoiceService = new ResponsiveVoiceService(this);
            responsiveVoiceService.initialize(new ResponsiveVoiceService.ResponsiveVoiceCallback() {
                @Override
                public void onReady() {
                    isResponsiveVoiceReady = true;
                    Log.d(TAG, "ResponsiveVoice service ready for enhanced male voice");

                    // Test ResponsiveVoice functionality
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            if (responsiveVoiceService != null) {
                                responsiveVoiceService.testResponsiveVoice();
                            }
                        }, 2000); // Test after 2 seconds
                    }
                }

                @Override
                public void onError(String error) {
                    isResponsiveVoiceReady = false;
                    Log.w(TAG, "ResponsiveVoice initialization failed: " + error + " (will use fallback TTS)");
                }

                @Override
                public void onSpeechStart() {
                    isSpeaking = true;
                    isUsingResponsiveVoice = true;
                    muteMicrophone();
                    Log.d(TAG, "ResponsiveVoice speech started");
                }

                @Override
                public void onSpeechEnd() {
                    isSpeaking = false;
                    isUsingResponsiveVoice = false;
                    // Unmute microphone after a short delay
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            unmuteMicrophone();
                        }, 500);
                    }
                    Log.d(TAG, "ResponsiveVoice speech ended");
                }

                @Override
                public void onSpeechError(String error) {
                    isSpeaking = false;
                    isUsingResponsiveVoice = false;
                    Log.e(TAG, "ResponsiveVoice speech error: " + error);
                    // Unmute microphone even on error
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            unmuteMicrophone();
                        }, 500);
                    }
                }
            });
            Log.d(TAG, "ResponsiveVoice service initialization started");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing ResponsiveVoice service", e);
            isResponsiveVoiceReady = false;
        }
    }

    /**
     * Update text-to-speech language based on current language setting
     */
    private void updateTextToSpeechLanguage() {
        if (textToSpeech != null && languageManager != null) {
            Locale speechLocale = languageManager.getSpeechLocale();
            if (speechLocale != null) {
                int result = textToSpeech.setLanguage(speechLocale);
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.e(TAG, "Language not supported for TTS: " + speechLocale.toString());
                    // Fall back to default language
                    textToSpeech.setLanguage(Locale.getDefault());
                } else {
                    Log.d(TAG, "TTS language set to: " + speechLocale.toString());
                }
            }
        }
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "VoiceRecognitionService started");
        startListening();
        return START_STICKY; // Restart if killed
    }
    
    private void startListening() {
        try {
            // Pre-flight checks for Samsung tablet compatibility
            if (!performPreflightChecks()) {
                android.util.Log.w(TAG, "🎤 Preflight checks failed - cannot start listening");
                return;
            }

            if (!isListening && speechRecognizer != null) {
                isListening = true;

                // Create fresh intent with current language settings and Samsung compatibility
                Intent recognizerIntent = createRecognizerIntentWithSamsungSupport();

                // Start listening with error handling
                speechRecognizer.startListening(recognizerIntent);

                // Broadcast listening state
                broadcastListeningState(true);

                Log.d(TAG, "Started listening for " + (isWakeWordMode ? "wake word" : "commands") + " with Samsung tablet support");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error starting listening", e);
            isListening = false;

            // Try recovery
            if (handler != null) {
                handler.postDelayed(() -> {
                    recreateSpeechRecognizerWithFallback();
                    if (!isListening) {
                        startListening();
                    }
                }, 2000);
            }
        }
    }

    /**
     * Perform preflight checks before starting speech recognition
     */
    private boolean performPreflightChecks() {
        try {
            // Check microphone permission
            if (!checkMicrophonePermission()) {
                android.util.Log.e(TAG, "🎤 Microphone permission not granted");
                return false;
            }

            // Check if speech recognition is available
            if (!SpeechRecognizer.isRecognitionAvailable(this)) {
                android.util.Log.e(TAG, "🎤 Speech recognition not available");
                return false;
            }

            // Check if speech recognizer exists
            if (speechRecognizer == null) {
                android.util.Log.w(TAG, "🎤 Speech recognizer is null - recreating");
                initializeSpeechRecognizer();
                return speechRecognizer != null;
            }

            // Check audio manager state
            if (audioManager != null && audioManager.isMicrophoneMute()) {
                android.util.Log.w(TAG, "🎤 Microphone is muted - unmuting for Samsung tablet");
                audioManager.setMicrophoneMute(false);
            }

            return true;

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error in preflight checks", e);
            return false;
        }
    }

    /**
     * Create recognizer intent with Samsung tablet specific settings
     */
    private Intent createRecognizerIntentWithSamsungSupport() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        intent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, getPackageName());

        // Samsung tablet specific settings
        String manufacturer = android.os.Build.MANUFACTURER.toLowerCase();
        if (manufacturer.contains("samsung")) {
            // Enhanced settings for Samsung devices
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 3000);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 3000);
            intent.putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS, 1500);

            android.util.Log.d(TAG, "🎤 Applied Samsung tablet specific recognition settings");
        }

        // Set language
        String languageCode = getLanguageCodeForSpeech();
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, languageCode);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, languageCode);

        // Enhanced audio settings for better microphone handling
        intent.putExtra(RecognizerIntent.EXTRA_AUDIO_SOURCE, android.media.MediaRecorder.AudioSource.MIC);

        return intent;
    }

    /**
     * Create RecognizerIntent with proper language settings
     */
    private Intent createRecognizerIntent() {
        Intent intent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        intent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
        intent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);
        intent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, getPackageName());

        // Set language based on current language manager setting
        if (languageManager != null) {
            String languageCode = getLanguageCodeForSpeech();
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, languageCode);
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, languageCode);
            Log.d(TAG, "🌍 Speech recognizer set to language: " + languageCode);
        } else {
            intent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "en-US");
        }

        return intent;
    }

    /**
     * Get proper language code for speech recognition
     */
    private String getLanguageCodeForSpeech() {
        if (languageManager == null) {
            return "en-US";
        }

        String currentLang = languageManager.getCurrentLanguage();
        switch (currentLang) {
            case "hi":
                return "hi-IN";  // Hindi (India)
            case "ml":
                return "ml-IN";  // Malayalam (India)
            case "ar":
                return "ar-SA";  // Arabic (Saudi Arabia)
            case "en":
            default:
                return "en-US";  // English (US)
        }
    }

    /**
     * Get idle timeout from settings (in milliseconds)
     */
    private long getIdleTimeoutMs() {
        if (sharedPreferences != null) {
            // Get mic duration from settings (default 4000ms)
            return sharedPreferences.getInt(SettingsFragment.PREF_MIC_DURATION, SettingsFragment.DEFAULT_MIC_DURATION);
        }
        return SILENCE_TIMEOUT; // fallback
    }

    /**
     * Start push-to-talk listening (walkie-talkie mode)
     * Stops normal listening and starts recording without processing
     */
    private void startPushToTalkListening() {
        try {
            android.util.Log.d(TAG, "🎤 Starting push-to-talk listening (walkie-talkie mode)");

            // Save current listening state to restore later
            wasListeningBeforePushToTalk = isListening;

            // Stop normal listening completely
            if (isListening && speechRecognizer != null) {
                speechRecognizer.stopListening();
                isListening = false;
            }

            // Set push-to-talk mode flags
            isPushToTalkMode = true;
            isPushToTalkActive = true;
            pendingPushToTalkText = null; // Clear any previous pending text

            // Start recording but don't process results yet
            startPushToTalkRecording();

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error starting push-to-talk listening", e);
        }
    }

    /**
     * Start recording for push-to-talk without processing results
     */
    private void startPushToTalkRecording() {
        try {
            if (speechRecognizer != null) {
                isListening = true;

                // Create fresh intent with current language settings
                Intent recognizerIntent = createRecognizerIntent();
                speechRecognizer.startListening(recognizerIntent);

                android.util.Log.d(TAG, "🎤 Push-to-talk recording started");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error starting push-to-talk recording", e);
        }
    }

    /**
     * Stop push-to-talk listening and process the recorded speech
     */
    private void stopPushToTalkListening() {
        try {
            android.util.Log.d(TAG, "🎤 Stopping push-to-talk listening (processing recorded speech)");

            // Stop current listening to trigger onResults
            if (isListening && speechRecognizer != null) {
                speechRecognizer.stopListening();
                isListening = false;
            }

            // Mark that we're no longer actively recording
            isPushToTalkActive = false;

            // Note: onResults will be called automatically when stopListening() is called
            // The actual processing will happen in onResults() method

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error stopping push-to-talk listening", e);
        }
    }

    private void stopListening() {
        if (isListening && speechRecognizer != null) {
            isListening = false;
            speechRecognizer.stopListening();

            // Broadcast listening state
            broadcastListeningState(false);

            Log.d(TAG, "Stopped listening");
        }
    }

    /**
     * Broadcast listening state to UI
     */
    private void broadcastListeningState(boolean isListening) {
        try {
            Intent intent = new Intent(ACTION_LISTENING_STATE);
            intent.putExtra(EXTRA_IS_LISTENING, isListening);
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
            Log.d(TAG, "Listening state broadcast: " + isListening);
        } catch (Exception e) {
            Log.e(TAG, "Error broadcasting listening state", e);
        }
    }
    
    @Override
    public void onReadyForSpeech(android.os.Bundle params) {
        Log.d(TAG, "Ready for speech");
    }
    
    @Override
    public void onBeginningOfSpeech() {
        Log.d(TAG, "Beginning of speech");
    }
    
    @Override
    public void onRmsChanged(float rmsdB) {
        // Audio level changed - can be used for visual feedback
    }
    
    @Override
    public void onBufferReceived(byte[] buffer) {
        // Audio buffer received
    }
    
    @Override
    public void onEndOfSpeech() {
        Log.d(TAG, "End of speech");
        isListening = false;
    }
    
    @Override
    public void onError(int error) {
        String errorMessage = getErrorMessage(error);
        Log.e(TAG, "Speech recognition error: " + error + " - " + errorMessage);
        isListening = false;

        // Handle specific error cases
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                handleAudioError();
                break;
            case SpeechRecognizer.ERROR_CLIENT:
                handleClientError();
                break;
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                handlePermissionError();
                break;
            case SpeechRecognizer.ERROR_NETWORK:
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                handleNetworkError();
                break;
            case SpeechRecognizer.ERROR_NO_MATCH:
                handleNoMatchError();
                break;
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                handleRecognizerBusyError();
                break;
            case SpeechRecognizer.ERROR_SERVER:
                handleServerError();
                break;
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                handleSpeechTimeoutError();
                break;
            default:
                handleGenericError(error);
                break;
        }
    }

    /**
     * Get human-readable error message for speech recognition errors
     */
    private String getErrorMessage(int error) {
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "Audio recording error - microphone may be unavailable";
            case SpeechRecognizer.ERROR_CLIENT:
                return "Client side error - speech recognizer issue";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "Insufficient permissions - microphone access denied";
            case SpeechRecognizer.ERROR_NETWORK:
                return "Network error - no internet connection";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "Network timeout - slow internet connection";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "No speech match found";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "Speech recognizer is busy";
            case SpeechRecognizer.ERROR_SERVER:
                return "Server error - speech recognition service unavailable";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "Speech input timeout - no speech detected";
            default:
                return "Unknown error: " + error;
        }
    }

    /**
     * Handle audio recording errors (common on Samsung tablets after updates)
     */
    private void handleAudioError() {
        Log.w(TAG, "🎤 Audio error detected - attempting microphone recovery for Samsung tablet compatibility");

        // Unmute microphone in case it's stuck muted
        unmuteMicrophone();

        // Try to reset audio manager settings
        resetAudioManagerSettings();

        // Recreate speech recognizer with enhanced settings
        recreateSpeechRecognizerWithFallback();

        // Restart with longer delay for Samsung tablets
        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    Log.d(TAG, "🎤 Attempting to restart listening after audio error recovery");
                    startListening();
                }
            }, 3000); // Longer delay for Samsung tablets
        }
    }

    /**
     * Handle client-side errors
     */
    private void handleClientError() {
        Log.w(TAG, "🎤 Client error - recreating speech recognizer");
        recreateSpeechRecognizerWithFallback();

        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 2000);
        }
    }

    /**
     * Handle permission errors
     */
    private void handlePermissionError() {
        Log.e(TAG, "🎤 Permission error - microphone access denied");

        // Broadcast permission error to UI
        Intent intent = new Intent("com.stemrobo.humanoid.MICROPHONE_PERMISSION_ERROR");
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);

        // Try to restart with delay in case permissions are restored
        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    Log.d(TAG, "🎤 Retrying after permission error");
                    startListening();
                }
            }, 5000);
        }
    }

    /**
     * Handle network-related errors
     */
    private void handleNetworkError() {
        Log.w(TAG, "🎤 Network error - will retry with offline recognition if available");

        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 2000);
        }
    }

    /**
     * Handle no match errors (normal during wake word detection)
     */
    private void handleNoMatchError() {
        // This is normal during wake word detection, just restart quickly
        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 500);
        }
    }

    /**
     * Handle recognizer busy errors
     */
    private void handleRecognizerBusyError() {
        Log.w(TAG, "🎤 Recognizer busy - waiting longer before retry");

        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 3000);
        }
    }

    /**
     * Handle server errors
     */
    private void handleServerError() {
        Log.w(TAG, "🎤 Server error - will retry with fallback settings");

        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 2000);
        }
    }

    /**
     * Handle speech timeout errors
     */
    private void handleSpeechTimeoutError() {
        // Normal timeout, just restart
        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    startListening();
                }
            }, 1000);
        }
    }

    /**
     * Handle generic/unknown errors
     */
    private void handleGenericError(int error) {
        Log.w(TAG, "🎤 Generic error " + error + " - attempting recovery");

        // Try comprehensive recovery for unknown errors
        unmuteMicrophone();
        resetAudioManagerSettings();

        if (handler != null) {
            handler.postDelayed(() -> {
                if (!isListening) {
                    recreateSpeechRecognizerWithFallback();
                    startListening();
                }
            }, 2000);
        }
    }
    
    @Override
    public void onResults(android.os.Bundle results) {
        ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
        if (matches != null && !matches.isEmpty()) {
            String spokenText = matches.get(0).toLowerCase();
            Log.d(TAG, "Recognized: " + spokenText);

            // Handle push-to-talk mode
            if (isPushToTalkMode) {
                android.util.Log.d(TAG, "🎤 Push-to-talk speech received: " + spokenText);

                // Process the speech immediately (no wake word needed)
                processUserInput(spokenText);

                // Reset push-to-talk mode and return to normal listening
                isPushToTalkMode = false;
                isWakeWordMode = true; // Return to wake word detection

                // Restart normal wake word listening after a short delay
                if (handler != null) {
                    handler.postDelayed(() -> {
                        if (!isPushToTalkMode) { // Only restart if not in push-to-talk mode again
                            startListening();
                        }
                    }, 1000);
                }
                return; // Exit early for push-to-talk
            }

            // Normal mode processing
            if (isWakeWordMode) {
                // Check for any wake word from any language (universal detection)
                boolean wakeWordDetected = false;
                if (languageManager != null) {
                    wakeWordDetected = languageManager.containsWakeWord(spokenText);
                    Log.d(TAG, "🎤 Checking for multi-language wake words in: " + spokenText);

                    if (wakeWordDetected) {
                        // Log which wake words are supported
                        String[] allWakeWords = languageManager.getAllWakeWordsAllLanguages();
                        Log.d(TAG, "🎤 Supported wake words: " + java.util.Arrays.toString(allWakeWords));
                    }
                } else {
                    // Fallback to English wake word
                    wakeWordDetected = spokenText.contains(WAKE_WORD);
                }

                if (wakeWordDetected) {
                    Log.d(TAG, "🎤 Multi-language wake word detected!");
                    isWakeWordMode = false;
                    speak("Yes, I'm listening");

                    // Start listening for commands
                    if (handler != null) {
                        handler.postDelayed(this::startListening, 2000);
                    }
                } else {
                    // Continue listening for wake word
                    startListening();
                }
            } else {
                // Process voice command or conversation
                processUserInput(spokenText);

                // Check if user wants to turn off robot
                if (spokenText.contains("turn off robot")) {
                    isWakeWordMode = true;
                    speak("Going back to sleep. Say 'Hey Robot' to wake me up.");
                    if (handler != null) {
                        handler.postDelayed(this::startListening, 2000);
                    }
                } else {
                    // Continue conversation mode - keep listening
                    if (handler != null) {
                        handler.postDelayed(this::startListening, 1000);
                    }
                }
            }
        }
    }
    
    @Override
    public void onPartialResults(android.os.Bundle partialResults) {
        // Partial results received - can be used for real-time feedback
        try {
            ArrayList<String> partialMatches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (partialMatches != null && !partialMatches.isEmpty()) {
                String partialText = partialMatches.get(0);
                updateLiveTranscription(partialText);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing partial results", e);
        }
    }

    /**
     * Update live transcription display with partial speech results
     */
    private void updateLiveTranscription(String partialText) {
        try {
            Intent intent = new Intent(ACTION_TRANSCRIPTION_UPDATE);
            intent.putExtra(EXTRA_PARTIAL_TEXT, partialText);
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);

            Log.d(TAG, "Live transcription updated: " + partialText);
        } catch (Exception e) {
            Log.e(TAG, "Error updating live transcription", e);
        }
    }
    
    @Override
    public void onEvent(int eventType, android.os.Bundle params) {
        // Speech recognition events
    }
    
    /**
     * Process user input - classify as LMS command, robot command, weather/news query, or general conversation
     */
    private void processUserInput(String input) {
        Log.d(TAG, "Processing user input: " + input);

        // Check if it's an LMS command first (highest priority)
        if (isLMSCommand(input)) {
            processLMSCommand(input);
        }
        // Check if it's a robot command
        else if (isRobotCommand(input)) {
            processRobotCommand(input);
        }
        // Check if it's a weather query
        else if (isWeatherQuery(input)) {
            processWeatherQuery(input);
        }
        // Check if it's a news query
        else if (isNewsQuery(input)) {
            processNewsQuery(input);
        }
        // Send to AI for general conversation
        else {
            processConversation(input);
        }
    }

    /**
     * Check if input is an LMS command (using LanguageManager for multilingual support)
     */
    private boolean isLMSCommand(String input) {
        if (languageManager != null) {
            return languageManager.isLMSCommand(input);
        }

        // Fallback to English-only detection
        String lowerInput = input.toLowerCase();

        // Check for LMS introduction commands
        if ((lowerInput.contains("introduce") && lowerInput.contains("lms")) ||
            (lowerInput.contains("lms") && lowerInput.contains("introduction")) ||
            (lowerInput.contains("lms") && lowerInput.contains("intro"))) {
            return true;
        }

        // Check for class-specific LMS commands (number + lms)
        if (lowerInput.contains("lms")) {
            // Look for numbers 1-12 in various formats
            for (int i = 1; i <= 12; i++) {
                if (lowerInput.contains(String.valueOf(i)) ||
                    lowerInput.contains(getOrdinalNumber(i)) ||
                    lowerInput.contains("class " + i) ||
                    lowerInput.contains("grade " + i)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if input is a robot command
     */
    private boolean isRobotCommand(String input) {
        if (languageManager != null) {
            return languageManager.isRobotCommand(input);
        }

        // Fallback to English commands
        String lowerInput = input.toLowerCase();
        for (String command : ROBOT_COMMANDS) {
            if (lowerInput.contains(command)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Process robot commands and send to ESP32
     */
    private void processRobotCommand(String command) {
        android.util.Log.d(TAG, "Processing robot command: " + command);

        // Translate command to English if using language manager
        String translatedCommand = command;
        if (languageManager != null) {
            translatedCommand = languageManager.translateCommandToEnglish(command);
        }

        String lowerCommand = translatedCommand.toLowerCase();

        // Basic movement commands - use timed movement with duration settings
        // Check backward first to avoid "forward" matching "backward"
        if (lowerCommand.contains("move backward") || lowerCommand.contains("go backward") || lowerCommand.contains("backward")) {
            Log.d(TAG, "Voice command: BACKWARD detected - " + lowerCommand);
            executeMovementWithDuration("backward", 0); // 0 = use preference setting
            speak("Moving backward");
        } else if (lowerCommand.contains("move forward") || lowerCommand.contains("go forward") || lowerCommand.contains("forward")) {
            Log.d(TAG, "Voice command: FORWARD detected - " + lowerCommand);
            executeMovementWithDuration("forward", 0); // 0 = use preference setting
            speak("Moving forward");
        } else if (lowerCommand.contains("turn left") || lowerCommand.contains("left")) {
            executeMovementWithDuration("turn_left", 0); // 0 = use preference setting
            speak("Turning left");
        } else if (lowerCommand.contains("turn right") || lowerCommand.contains("right")) {
            executeMovementWithDuration("turn_right", 0); // 0 = use preference setting
            speak("Turning right");
        } else if (lowerCommand.contains("stop")) {
            sendMotorCommand("S");
            speak("Stopping");
        }

        // Mecanum wheel omnidirectional movement commands (only available in mecanum mode)
        else if ("mecanum".equals(currentRobotType) && lowerCommand.contains("slide left")) {
            executeMovementWithDuration("slide_left", 0); // 0 = use preference setting
            speak("Sliding left");
        } else if ("mecanum".equals(currentRobotType) && lowerCommand.contains("slide right")) {
            executeMovementWithDuration("slide_right", 0); // 0 = use preference setting
            speak("Sliding right");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("diagonal front left") || lowerCommand.contains("move diagonal front left"))) {
            executeMovementWithDuration("diagonal_front_left", 0); // 0 = use preference setting
            speak("Moving diagonal front left");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("diagonal front right") || lowerCommand.contains("move diagonal front right"))) {
            executeMovementWithDuration("diagonal_front_right", 0); // 0 = use preference setting
            speak("Moving diagonal front right");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("diagonal back left") || lowerCommand.contains("move diagonal back left"))) {
            executeMovementWithDuration("diagonal_back_left", 0); // 0 = use preference setting
            speak("Moving diagonal back left");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("diagonal back right") || lowerCommand.contains("move diagonal back right"))) {
            executeMovementWithDuration("diagonal_back_right", 0); // 0 = use preference setting
            speak("Moving diagonal back right");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("rotate left") || lowerCommand.contains("spin left"))) {
            executeMovementWithDuration("rotate_left", 0); // 0 = use preference setting
            speak("Rotating left");
        } else if ("mecanum".equals(currentRobotType) && (lowerCommand.contains("rotate right") || lowerCommand.contains("spin right"))) {
            executeMovementWithDuration("rotate_right", 0); // 0 = use preference setting
            speak("Rotating right");
        }

        // Arm commands
        else if (lowerCommand.contains("wave")) {
            sendServoCommand("WAVE");
            speak("Waving hello");
        } else if (lowerCommand.contains("point")) {
            sendServoCommand("POINT");
            speak("Pointing");
        } else if (lowerCommand.contains("rest")) {
            sendServoCommand("REST");
            speak("Going to rest position");
        }

        // Head commands (not implemented in ESP32 yet, but keeping for future)
        else if (lowerCommand.contains("center head")) {
            sendServoCommand("center_head");
            speak("Centering head");
        } else if (lowerCommand.contains("look up")) {
            sendServoCommand("look_up");
            speak("Looking up");
        } else if (lowerCommand.contains("look down")) {
            sendServoCommand("look_down");
            speak("Looking down");
        }

        // Preset execution commands
        else if (lowerCommand.contains("execute preset") || lowerCommand.contains("run preset")) {
            handlePresetCommand(lowerCommand);
        } else if (lowerCommand.contains("stop preset") || lowerCommand.contains("stop execution")) {
            stopPresetExecution();
        }

        // Broadcast command to UI
        broadcastVoiceResult(command, "Command executed");
    }

    /**
     * Process LMS commands and launch appropriate video player (with multilingual support)
     */
    private void processLMSCommand(String command) {
        Log.d(TAG, "Processing LMS command: " + command);

        // Use LanguageManager to detect LMS commands
        if (languageManager != null) {
            String lowerCommand = command.toLowerCase();
            Log.d(TAG, "Checking LMS command in language: " + languageManager.getCurrentLanguage());

            // Check for LMS introduction commands
            boolean isIntroCommand = isLMSIntroductionCommand(lowerCommand);
            Log.d(TAG, "Is LMS introduction command: " + isIntroCommand);

            if (isIntroCommand) {
                String response = languageManager.getLMSIntroResponse();
                Log.d(TAG, "Playing LMS intro video with response: " + response);
                speak(response);
                launchLMSIntroVideo();
                broadcastVoiceResult(command, "Playing LMS introduction video");
                return;
            }

            // Check for class-specific LMS commands using LanguageManager
            int classNumber = languageManager.extractClassNumber(command);
            Log.d(TAG, "Extracted class number: " + classNumber);
            if (classNumber > 0 && classNumber <= 12) {
                String response = languageManager.getLMSClassResponse(classNumber);
                Log.d(TAG, "Playing LMS class " + classNumber + " video with response: " + response);
                speak(response);
                launchLMSClassVideo(classNumber);
                broadcastVoiceResult(command, "Playing class " + classNumber + " LMS video");
                return;
            }

            // Use LanguageManager for error response
            String errorResponse = languageManager.getLMSErrorResponse();
            Log.d(TAG, "LMS command not recognized, error response: " + errorResponse);
            speak(errorResponse);
            broadcastVoiceResult(command, "LMS command not recognized");
            return;
        }

        // Fallback to English-only processing if LanguageManager is not available
        String lowerCommand = command.toLowerCase();

        // Check for LMS introduction
        if ((lowerCommand.contains("introduce") && lowerCommand.contains("lms")) ||
            (lowerCommand.contains("lms") && lowerCommand.contains("introduction")) ||
            (lowerCommand.contains("lms") && lowerCommand.contains("intro"))) {

            speak("Starting STEM-Xpert LMS introduction video");
            launchLMSIntroVideo();
            broadcastVoiceResult(command, "Playing LMS introduction video");
            return;
        }

        // Check for class-specific LMS commands
        if (lowerCommand.contains("lms")) {
            int classNumber = extractClassNumber(lowerCommand);
            if (classNumber > 0 && classNumber <= 12) {
                speak("Starting class " + classNumber + " LMS video");
                launchLMSClassVideo(classNumber);
                broadcastVoiceResult(command, "Playing class " + classNumber + " LMS video");
                return;
            }
        }

        // Fallback if LMS command not recognized
        speak("I didn't understand which LMS content you want. Please specify the class number or say LMS introduction.");
        broadcastVoiceResult(command, "LMS command not recognized");
    }

    /**
     * Check if input is a weather query
     */
    private boolean isWeatherQuery(String input) {
        String lowerInput = input.toLowerCase();
        return lowerInput.contains("weather") || lowerInput.contains("temperature") ||
               lowerInput.contains("climate") || lowerInput.contains("hot") ||
               lowerInput.contains("cold") || lowerInput.contains("rain");
    }

    /**
     * Check if input is a news query
     */
    private boolean isNewsQuery(String input) {
        String lowerInput = input.toLowerCase();
        return lowerInput.contains("news") || lowerInput.contains("headlines") ||
               lowerInput.contains("latest") || lowerInput.contains("current events");
    }

    /**
     * Process weather query with location detection
     */
    private void processWeatherQuery(String input) {
        Log.d(TAG, "Processing weather query: " + input);

        if (enhancedGeminiAI != null && enhancedGeminiAI.getRealTimeDataService() != null) {
            enhancedGeminiAI.getRealTimeDataService().getWeatherDataForLocation(input,
                new SimpleRealTimeDataService.RealTimeDataCallback() {
                    @Override
                    public void onSuccess(String weatherData) {
                        speak(weatherData);
                        broadcastVoiceResult(input, weatherData);
                        Log.d(TAG, "Weather data retrieved successfully");
                    }

                    @Override
                    public void onError(String error) {
                        String fallbackResponse = "I'm having trouble getting weather information right now.";
                        speak(fallbackResponse);
                        broadcastVoiceResult(input, fallbackResponse);
                        Log.e(TAG, "Weather query failed: " + error);
                    }
                });
        } else {
            String fallbackResponse = "Weather service is not available right now.";
            speak(fallbackResponse);
            broadcastVoiceResult(input, fallbackResponse);
        }
    }

    /**
     * Process news query
     */
    private void processNewsQuery(String input) {
        Log.d(TAG, "Processing news query: " + input);

        if (enhancedGeminiAI != null && enhancedGeminiAI.getRealTimeDataService() != null) {
            enhancedGeminiAI.getRealTimeDataService().getNewsData(
                new SimpleRealTimeDataService.RealTimeDataCallback() {
                    @Override
                    public void onSuccess(String newsData) {
                        speak(newsData);
                        broadcastVoiceResult(input, newsData);
                        Log.d(TAG, "News data retrieved successfully");
                    }

                    @Override
                    public void onError(String error) {
                        String fallbackResponse = "I'm having trouble getting news information right now.";
                        speak(fallbackResponse);
                        broadcastVoiceResult(input, fallbackResponse);
                        Log.e(TAG, "News query failed: " + error);
                    }
                });
        } else {
            String fallbackResponse = "News service is not available right now.";
            speak(fallbackResponse);
            broadcastVoiceResult(input, fallbackResponse);
        }
    }

    /**
     * Process general conversation using AI with conversation memory
     */
    private void processConversation(String input) {
        android.util.Log.d(TAG, "Processing conversation with memory: " + input);

        // Get current language (make it final for inner class access)
        final String currentLanguage;
        if (languageManager != null) {
            currentLanguage = languageManager.getCurrentLanguage();
        } else {
            currentLanguage = null;
        }

        // Initialize conversation memory if needed
        if (conversationMemory != null) {
            // Start or continue conversation
            conversationMemory.startConversation(currentLanguage);

            // Add user message to conversation history
            conversationMemory.addUserMessage(input, currentLanguage);
        }

        // Get AI response using Enhanced Gemini with conversation context and real-time data
        if (enhancedGeminiAI != null && conversationMemory != null) {
            // Get conversation context for AI
            String conversationContext = conversationMemory.getConversationContext();

            // Use enhanced AI service with real-time data integration
            enhancedGeminiAI.getEnhancedAIResponse(input, currentLanguage, conversationContext,
                new GeminiAIService.AIResponseCallback() {
                    @Override
                    public void onSuccess(String response) {
                        // Add AI response to conversation memory
                        if (conversationMemory != null) {
                            conversationMemory.addAIResponse(response, currentLanguage, 0, "enhanced_with_realtime_data");
                        }
                        speak(response);
                        broadcastVoiceResult(input, response);

                        android.util.Log.d(TAG, "Enhanced AI response with real-time data generated successfully");
                    }

                    @Override
                    public void onError(String error) {
                        android.util.Log.w(TAG, "Enhanced AI failed, falling back to regular AI: " + error);

                        // Fallback to regular AI if enhanced fails
                        if (geminiAI != null) {
                            geminiAI.getAIResponseWithContext(input, currentLanguage, conversationContext,
                                new GeminiAIService.AIResponseCallback() {
                                    @Override
                                    public void onSuccess(String response) {
                                        if (conversationMemory != null) {
                                            conversationMemory.addAIResponse(response, currentLanguage, 0, "");
                                        }
                                        speak(response);
                                        broadcastVoiceResult(input, response);
                                    }

                                    @Override
                                    public void onError(String fallbackError) {
                                        String fallbackResponse = "I'm having trouble thinking right now. Could you repeat that?";
                                        speak(fallbackResponse);
                                        broadcastVoiceResult(input, fallbackResponse);
                                    }
                                });
                        } else {
                            String fallbackResponse = "I'm having trouble thinking right now. Could you repeat that?";
                            speak(fallbackResponse);
                            broadcastVoiceResult(input, fallbackResponse);
                        }
                    }
                });
        } else if (geminiAI != null) {
            // Fallback to regular AI response without memory
            geminiAI.getAIResponse(input, currentLanguage, new GeminiAIService.AIResponseCallback() {
                @Override
                public void onSuccess(String response) {
                    speak(response);
                    broadcastVoiceResult(input, response);
                }

                @Override
                public void onError(String error) {
                    String fallbackResponse = "I'm having trouble thinking right now. Could you repeat that?";
                    speak(fallbackResponse);
                    broadcastVoiceResult(input, fallbackResponse);
                }
            });
        } else {
            // Use fallback AI responses when Gemini is not available
            String fallbackResponse = generateFallbackResponse(input);
            speak(fallbackResponse);
            broadcastVoiceResult(input, fallbackResponse);
        }
    }

    /**
     * Generate fallback AI response when Gemini is not available
     */
    private String generateFallbackResponse(String input) {
        String lowerInput = input.toLowerCase().trim();

        // Greeting responses
        if (lowerInput.contains("hello") || lowerInput.contains("hi") || lowerInput.contains("hey")) {
            return "Hello! I'm Guruji from STEM-Xpert. How can I help you with robotics or programming today?";
        }

        // How are you responses
        if (lowerInput.contains("how are you") || lowerInput.contains("how do you feel")) {
            return "I'm doing great! My systems are running smoothly. How are you doing?";
        }

        // Name responses
        if (lowerInput.contains("what's your name") || lowerInput.contains("who are you")) {
            return "My name is Guruji, an AI assistant created by STEM-Xpert company. I help with robotics, programming, and educational content.";
        }

        // Help responses
        if (lowerInput.contains("help") || lowerInput.contains("what can you do")) {
            return "I can help with robotics, programming, and educational content. I can also control my movements and answer STEM questions.";
        }

        // Movement commands
        if (lowerInput.contains("move") || lowerInput.contains("walk") || lowerInput.contains("forward")) {
            return "I'm processing your movement command. My motors are responding!";
        }

        // Default response
        return "That's interesting! I represent STEM-Xpert's commitment to quality education. What would you like to explore in robotics or programming?";
    }

    /**
     * Send motor command to ESP32 (simplified without speed control)
     */
    private void sendMotorCommand(String action) {
        if (esp32Manager != null) {
            esp32Manager.sendMotorCommand(action);
        }
    }

    /**
     * Send servo command to ESP32 (simplified without parameters)
     */
    private void sendServoCommand(String action) {
        if (esp32Manager != null) {
            esp32Manager.sendServoCommand(action);
        }
    }

    /**
     * Execute movement with duration for voice commands
     */
    private void executeMovementWithDuration(String direction, int duration) {
        if (esp32Manager == null) return;

        // Get duration preferences
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        final int actualDuration; // Make final for inner class access

        if (direction.equals("turn_left") || direction.equals("turn_right")) {
            actualDuration = prefs.getInt("voice_side_duration", 2000); // Default 2 seconds
        } else if (direction.equals("slide_left") || direction.equals("slide_right")) {
            actualDuration = prefs.getInt("voice_slide_duration", 2500); // Default 2.5 seconds
        } else if (direction.equals("diagonal_front_left") || direction.equals("diagonal_front_right") ||
                   direction.equals("diagonal_back_left") || direction.equals("diagonal_back_right")) {
            actualDuration = prefs.getInt("voice_diagonal_duration", 3000); // Default 3 seconds
        } else if (direction.equals("rotate_left") || direction.equals("rotate_right")) {
            actualDuration = prefs.getInt("voice_rotate_duration", 2000); // Default 2 seconds
        } else {
            actualDuration = prefs.getInt("voice_forward_duration", 3000); // Default 3 seconds
        }

        // Use provided duration if specified, otherwise use preference
        final int finalDuration = (duration > 0) ? duration : actualDuration;

        // Map direction to ESP32 command format with robot type awareness
        String esp32Command;

        // Log the direction for debugging
        Log.d(TAG, "executeMovementWithDuration: direction=" + direction + ", robotType=" + currentRobotType);

        // Basic movement commands (available for both robot types)
        switch (direction) {
            case "forward":
                esp32Command = "F";
                break;
            case "backward":
                esp32Command = "B";
                break;
            case "turn_left":
                esp32Command = "L";
                break;
            case "turn_right":
                esp32Command = "R";
                break;
            case "stop":
                esp32Command = "S";
                break;
            default:
                // Handle mecanum-specific commands only if robot type is mecanum
                if ("mecanum".equals(currentRobotType)) {
                    switch (direction) {
                        case "slide_left":
                            esp32Command = "SL";
                            break;
                        case "slide_right":
                            esp32Command = "SR";
                            break;
                        case "diagonal_front_left":
                            esp32Command = "DFL";
                            break;
                        case "diagonal_front_right":
                            esp32Command = "DFR";
                            break;
                        case "diagonal_back_left":
                            esp32Command = "DBL";
                            break;
                        case "diagonal_back_right":
                            esp32Command = "DBR";
                            break;
                        case "rotate_left":
                            esp32Command = "ROT_L";
                            break;
                        case "rotate_right":
                            esp32Command = "ROT_R";
                            break;
                        default:
                            esp32Command = "S"; // Default to stop
                            break;
                    }
                } else {
                    // For normal robot type, ignore mecanum-specific commands
                    Log.w(TAG, "Ignoring mecanum command '" + direction + "' for normal robot type");
                    esp32Command = "S"; // Default to stop
                }
                break;
        }

        // Send movement command using ESP32CommunicationManager
        esp32Manager.sendMotorCommand(esp32Command);

        // Initialize movement handler if not already done
        if (movementHandler == null) {
            movementHandler = new Handler(Looper.getMainLooper());
        }

        // Cancel any existing stop movement runnable
        if (stopMovementRunnable != null) {
            movementHandler.removeCallbacks(stopMovementRunnable);
        }

        // Create new stop movement runnable
        stopMovementRunnable = new Runnable() {
            @Override
            public void run() {
                esp32Manager.sendMotorCommand("S"); // Stop command
                android.util.Log.d(TAG, "Voice command movement stopped after " + finalDuration + "ms");
            }
        };

        // Schedule stop movement after duration
        movementHandler.postDelayed(stopMovementRunnable, finalDuration);

        android.util.Log.d(TAG, "Voice command movement started: " + direction + " for " + finalDuration + "ms");
    }

    /**
     * Check if command is an LMS introduction command in any supported language
     */
    private boolean isLMSIntroductionCommand(String lowerCommand) {
        // English introduction keywords
        boolean hasEnglishIntro = (lowerCommand.contains("introduce") && lowerCommand.contains("lms")) ||
                                  (lowerCommand.contains("lms") && lowerCommand.contains("introduction")) ||
                                  (lowerCommand.contains("lms") && lowerCommand.contains("intro"));

        // Malayalam introduction keywords
        boolean hasMalayalamIntro = (lowerCommand.contains("എൽഎംഎസ്") &&
                                    (lowerCommand.contains("പരിചയപ്പെടുത്തുക") ||
                                     lowerCommand.contains("പരിചയം") ||
                                     lowerCommand.contains("ആമുഖം") ||
                                     lowerCommand.contains("കാണിക്കുക") ||
                                     lowerCommand.contains("ആരംഭിക്കുക")));

        // Hindi introduction keywords
        boolean hasHindiIntro = (lowerCommand.contains("एलएमएस") &&
                                (lowerCommand.contains("परिचय") ||
                                 lowerCommand.contains("दिखाओ") ||
                                 lowerCommand.contains("शुरू करो") ||
                                 lowerCommand.contains("प्रस्तुति")));

        // Arabic introduction keywords
        boolean hasArabicIntro = (lowerCommand.contains("إل إم إس") &&
                                 (lowerCommand.contains("تعريف") ||
                                  lowerCommand.contains("مقدمة") ||
                                  lowerCommand.contains("أظهر") ||
                                  lowerCommand.contains("ابدأ") ||
                                  lowerCommand.contains("عرض")));

        return hasEnglishIntro || hasMalayalamIntro || hasHindiIntro || hasArabicIntro;
    }

    /**
     * Get ordinal number string (1st, 2nd, 3rd, etc.)
     */
    private String getOrdinalNumber(int number) {
        switch (number) {
            case 1: return "1st";
            case 2: return "2nd";
            case 3: return "3rd";
            case 4: return "4th";
            case 5: return "5th";
            case 6: return "6th";
            case 7: return "7th";
            case 8: return "8th";
            case 9: return "9th";
            case 10: return "10th";
            case 11: return "11th";
            case 12: return "12th";
            default: return number + "th";
        }
    }

    /**
     * Extract class number from LMS command
     */
    private int extractClassNumber(String command) {
        String lowerCommand = command.toLowerCase();

        // Look for numbers 1-12 in various formats
        for (int i = 1; i <= 12; i++) {
            if (lowerCommand.contains(String.valueOf(i)) ||
                lowerCommand.contains(getOrdinalNumber(i).toLowerCase()) ||
                lowerCommand.contains("class " + i) ||
                lowerCommand.contains("grade " + i)) {
                return i;
            }
        }

        return 0; // Not found
    }

    /**
     * Launch LMS introduction video player
     */
    private void launchLMSIntroVideo() {
        try {
            Intent intent = new Intent(this, LMSVideoPlayerActivity.class);
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_PATH, "LMS-intro.mp4");
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_TITLE, "STEM-Xpert LMS Introduction");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);

            Log.d(TAG, "Launched LMS introduction video");
        } catch (Exception e) {
            Log.e(TAG, "Error launching LMS introduction video", e);
        }
    }

    /**
     * Launch LMS class video player
     */
    private void launchLMSClassVideo(int classNumber) {
        try {
            Intent intent = new Intent(this, LMSYouTubePlayerActivity.class);
            intent.putExtra(LMSYouTubePlayerActivity.EXTRA_CLASS_NUMBER, classNumber);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);

            Log.d(TAG, "Launched LMS class " + classNumber + " video");
        } catch (Exception e) {
            Log.e(TAG, "Error launching LMS class video", e);
        }
    }

    /**
     * Broadcast voice result to UI
     */
    private void broadcastVoiceResult(String userText, String aiResponse) {
        Intent intent = new Intent(ACTION_VOICE_RESULT);
        intent.putExtra(EXTRA_VOICE_TEXT, userText);
        intent.putExtra(EXTRA_AI_RESPONSE, aiResponse);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    private void speak(String text) {
        if (isExternallyMuted) {
            android.util.Log.d(TAG, "🔇 Speech muted externally: " + text);
            return;
        }

        // Check if we should use ResponsiveVoice for male voice
        String voiceGender = sharedPreferences.getString(SettingsFragment.PREF_VOICE_GENDER, SettingsFragment.DEFAULT_VOICE_GENDER);

        if ("male".equals(voiceGender) && isResponsiveVoiceReady && responsiveVoiceService != null) {
            // Use ResponsiveVoice for male voice
            speakWithResponsiveVoice(text);
        } else {
            // Use standard TTS (for female voice or when ResponsiveVoice is not available)
            speakWithStandardTTS(text);
        }
    }

    /**
     * Speak using ResponsiveVoice for enhanced male voice quality
     */
    private void speakWithResponsiveVoice(String text) {
        try {
            android.util.Log.d(TAG, "=== RESPONSIVEVOICE SPEAK ATTEMPT ===");
            android.util.Log.d(TAG, "Text to speak: " + text);
            android.util.Log.d(TAG, "ResponsiveVoice ready: " + isResponsiveVoiceReady);
            android.util.Log.d(TAG, "ResponsiveVoice service: " + (responsiveVoiceService != null ? "Available" : "NULL"));

            // Get speech settings
            float speechSpeed = sharedPreferences.getFloat(SettingsFragment.PREF_SPEECH_SPEED, SettingsFragment.DEFAULT_SPEECH_SPEED);
            android.util.Log.d(TAG, "Speech speed: " + speechSpeed);

            // Use ResponsiveVoice with male voice
            responsiveVoiceService.speak(text, "male", speechSpeed, 1.0f);
            android.util.Log.d(TAG, "🔊 ResponsiveVoice speak() method called successfully");
            android.util.Log.d(TAG, "🔊 Speaking with ResponsiveVoice (Male): " + text);

        } catch (Exception e) {
            android.util.Log.e(TAG, "❌ Error with ResponsiveVoice, falling back to standard TTS", e);
            android.util.Log.e(TAG, "Exception details: " + e.getMessage());
            e.printStackTrace();
            // Fallback to standard TTS
            speakWithStandardTTS(text);
        }
    }

    /**
     * Speak using standard Android TTS
     */
    private void speakWithStandardTTS(String text) {
        if (textToSpeech != null) {
            // Mute microphone to prevent audio feedback loop (don't stop speech recognizer)
            muteMicrophone();
            isSpeaking = true;

            // Apply current voice gender settings before speaking
            configureVoiceGender();

            // Create parameters with utterance ID for completion tracking
            HashMap<String, String> params = new HashMap<>();
            params.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, UTTERANCE_ID);

            textToSpeech.speak(text, TextToSpeech.QUEUE_FLUSH, params);
            android.util.Log.d(TAG, "🔊 Speaking with Standard TTS: " + text + " (Microphone muted to prevent feedback)");
        }
    }

    /**
     * Mute microphone to prevent audio feedback during TTS
     */
    private void muteMicrophone() {
        try {
            if (audioManager != null) {
                // Mute microphone at system level
                audioManager.setMicrophoneMute(true);
                android.util.Log.d(TAG, "🔇 Microphone muted for TTS feedback prevention");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error muting microphone", e);
        }
    }

    /**
     * Unmute microphone after TTS completion
     */
    private void unmuteMicrophone() {
        try {
            if (audioManager != null) {
                // Unmute microphone at system level
                audioManager.setMicrophoneMute(false);
                android.util.Log.d(TAG, "🎤 Microphone unmuted after TTS completion");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error unmuting microphone", e);
        }
    }

    /**
     * Reset audio manager settings for Samsung tablet compatibility
     */
    private void resetAudioManagerSettings() {
        try {
            if (audioManager != null) {
                // Ensure microphone is unmuted
                audioManager.setMicrophoneMute(false);

                // Reset audio mode to normal
                audioManager.setMode(AudioManager.MODE_NORMAL);

                // Clear any audio focus issues
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    // For newer Android versions, abandon any existing audio focus
                    audioManager.abandonAudioFocusRequest(null);
                } else {
                    // For older versions
                    audioManager.abandonAudioFocus(null);
                }

                android.util.Log.d(TAG, "🎤 Audio manager settings reset for Samsung tablet compatibility");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error resetting audio manager settings", e);
        }
    }

    /**
     * Recreate speech recognizer with enhanced fallback for Samsung tablets
     */
    private void recreateSpeechRecognizerWithFallback() {
        try {
            // Destroy existing recognizer
            if (speechRecognizer != null) {
                speechRecognizer.destroy();
                speechRecognizer = null;
            }

            // Wait a moment for cleanup
            Thread.sleep(500);

            // Check if speech recognition is available
            if (!SpeechRecognizer.isRecognitionAvailable(this)) {
                android.util.Log.e(TAG, "🎤 Speech recognition not available on this device");
                return;
            }

            // Create new speech recognizer with enhanced settings
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            if (speechRecognizer == null) {
                android.util.Log.e(TAG, "🎤 Failed to create speech recognizer");
                return;
            }

            speechRecognizer.setRecognitionListener(this);
            updateSpeechRecognizerLanguage();

            android.util.Log.d(TAG, "🎤 Speech recognizer recreated successfully with Samsung tablet compatibility");

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error recreating speech recognizer", e);
        }
    }
    
    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            // Set up TTS completion listener to prevent audio feedback
            textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                @Override
                public void onStart(String utteranceId) {
                    android.util.Log.d(TAG, "🔊 TTS started: " + utteranceId);
                }

                @Override
                public void onDone(String utteranceId) {
                    android.util.Log.d(TAG, "✅ TTS completed: " + utteranceId);
                    isSpeaking = false;

                    // Unmute microphone after a short delay to prevent feedback
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            unmuteMicrophone();
                        }, 500); // 500ms delay to ensure audio output is completely finished
                    }
                }

                @Override
                public void onError(String utteranceId) {
                    android.util.Log.e(TAG, "❌ TTS error: " + utteranceId);
                    isSpeaking = false;

                    // Unmute microphone even on error
                    if (handler != null) {
                        handler.postDelayed(() -> {
                            unmuteMicrophone();
                        }, 500);
                    }
                }
            });

            int result = textToSpeech.setLanguage(Locale.getDefault());
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                android.util.Log.e(TAG, "Language not supported for TTS");
            } else {
                android.util.Log.d(TAG, "TextToSpeech initialized successfully with feedback prevention");

                // Configure voice gender based on settings
                configureVoiceGender();

                // Test ResponsiveVoice if male voice is selected
                testResponsiveVoiceIntegration();

                speak("Robot voice system ready");
            }
        } else {
            android.util.Log.e(TAG, "TextToSpeech initialization failed");
        }
    }

    /**
     * Configure voice gender using multiple free methods for better male voice quality
     */
    private void configureVoiceGender() {
        if (textToSpeech != null && sharedPreferences != null) {
            String voiceGender = sharedPreferences.getString(SettingsFragment.PREF_VOICE_GENDER, SettingsFragment.DEFAULT_VOICE_GENDER);

            if ("male".equals(voiceGender)) {
                configureMaleVoice();
            } else {
                configureFemaleVoice();
            }
        }
    }

    /**
     * Configure male voice using multiple free techniques for better quality
     */
    private void configureMaleVoice() {
        try {
            // Method 1: Try to use the best available TTS engine
            logAvailableTTSEngines();

            // Method 2: Enhanced pitch and rate manipulation with audio effects simulation
            applyEnhancedMaleVoiceSettings();

            android.util.Log.d(TAG, "🎤 MALE voice configured using enhanced audio processing");

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error configuring male voice: " + e.getMessage());
            // Fallback to basic configuration
            textToSpeech.setPitch(0.6f);
            textToSpeech.setSpeechRate(0.85f);
        }
    }

    /**
     * Apply enhanced male voice settings using multiple audio parameters
     */
    private void applyEnhancedMaleVoiceSettings() {
        // Extremely low pitch for deep male voice
        textToSpeech.setPitch(0.4f);  // Much lower than before

        // Slower speech rate for masculine effect
        float speechSpeed = sharedPreferences.getFloat(SettingsFragment.PREF_SPEECH_SPEED, SettingsFragment.DEFAULT_SPEECH_SPEED);
        textToSpeech.setSpeechRate(speechSpeed * 0.75f); // Even slower

        // Try to set additional audio parameters if available
        try {
            // Some TTS engines support additional parameters
            HashMap<String, String> params = new HashMap<>();
            params.put(TextToSpeech.Engine.KEY_PARAM_VOLUME, "0.9"); // Slightly lower volume for depth

            // Try to apply these parameters to the TTS engine
            // This is a best-effort approach - some engines may ignore these

        } catch (Exception e) {
            android.util.Log.w(TAG, "Could not apply additional audio parameters: " + e.getMessage());
        }

        android.util.Log.d(TAG, "🎤 Enhanced MALE voice settings applied (pitch: 0.4, rate: " + (speechSpeed * 0.75f) + ")");
    }

    /**
     * Log available TTS engines for debugging
     */
    private void logAvailableTTSEngines() {
        try {
            List<TextToSpeech.EngineInfo> engines = textToSpeech.getEngines();
            android.util.Log.d(TAG, "🎤 Available TTS engines:");

            for (TextToSpeech.EngineInfo engine : engines) {
                android.util.Log.d(TAG, "🎤 Engine: " + engine.name + " (label: " + engine.label + ")");
            }

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error logging TTS engines: " + e.getMessage());
        }
    }

    /**
     * Configure female voice
     */
    private void configureFemaleVoice() {
        try {
            // For female voice, use standard configuration
            textToSpeech.setPitch(1.1f);  // Slightly higher pitch
            float speechSpeed = sharedPreferences.getFloat(SettingsFragment.PREF_SPEECH_SPEED, SettingsFragment.DEFAULT_SPEECH_SPEED);
            textToSpeech.setSpeechRate(speechSpeed);

            android.util.Log.d(TAG, "🎤 FEMALE voice configured (pitch: 1.1, rate: " + speechSpeed + ")");

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error configuring female voice: " + e.getMessage());
        }
    }



    // Intent extras for mute control
    public static final String EXTRA_IS_MUTED = "is_muted";

    /**
     * Set external mute state for TTS
     */
    public void setExternalMute(boolean muted) {
        isExternallyMuted = muted;
        android.util.Log.d(TAG, "External mute state changed: " + (muted ? "MUTED" : "UNMUTED"));

        // Broadcast mute state change
        Intent intent = new Intent(ACTION_MUTE_STATE_CHANGED);
        intent.putExtra(EXTRA_IS_MUTED, muted);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Get current external mute state
     */
    public boolean isExternallyMuted() {
        return isExternallyMuted;
    }

    /**
     * Clear conversation memory
     */
    private void clearConversationMemory() {
        try {
            if (conversationMemory != null) {
                conversationMemory.clearConversationMemory();
                android.util.Log.d(TAG, "Conversation memory cleared");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error clearing conversation memory", e);
        }
    }

    private void setupRobotTypeReceiver() {
        robotTypeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if ("com.stemrobo.humanoid.ROBOT_TYPE_CHANGED".equals(intent.getAction())) {
                    String newRobotType = intent.getStringExtra("robot_type");
                    if (newRobotType != null && !newRobotType.equals(currentRobotType)) {
                        currentRobotType = newRobotType;
                        Log.d(TAG, "Robot type changed to: " + currentRobotType);
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter("com.stemrobo.humanoid.ROBOT_TYPE_CHANGED");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(robotTypeReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(robotTypeReceiver, filter);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // Ensure microphone is unmuted when service is destroyed
        unmuteMicrophone();

        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }

        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }

        // Unregister broadcast receiver
        if (robotTypeReceiver != null) {
            try {
                unregisterReceiver(robotTypeReceiver);
            } catch (Exception e) {
                Log.e(TAG, "Error unregistering robot type receiver", e);
            }
        }

        // Cleanup ResponsiveVoice service
        if (responsiveVoiceService != null) {
            responsiveVoiceService.destroy();
            responsiveVoiceService = null;
            isResponsiveVoiceReady = false;
            isUsingResponsiveVoice = false;
        }

        // Unregister broadcast receivers
        try {
            unregisterReceiver(voiceSettingsReceiver);
            unregisterReceiver(microphoneTestReceiver);
        } catch (Exception e) {
            android.util.Log.w(TAG, "Error unregistering receivers: " + e.getMessage());
        }

        android.util.Log.d(TAG, "VoiceRecognitionService destroyed");
    }

    /**
     * Test ResponsiveVoice integration when male voice is selected
     */
    private void testResponsiveVoiceIntegration() {
        try {
            String voiceGender = sharedPreferences.getString(SettingsFragment.PREF_VOICE_GENDER, SettingsFragment.DEFAULT_VOICE_GENDER);

            if ("male".equals(voiceGender)) {
                android.util.Log.d(TAG, "🎤 Male voice selected - ResponsiveVoice integration active");

                if (isResponsiveVoiceReady) {
                    android.util.Log.d(TAG, "✅ ResponsiveVoice is ready for enhanced male voice");
                } else {
                    android.util.Log.w(TAG, "⚠️ ResponsiveVoice not ready - will use fallback TTS for male voice");
                }
            } else {
                android.util.Log.d(TAG, "🎤 Female voice selected - using standard TTS");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error testing ResponsiveVoice integration", e);
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // Not a bound service
    }

    // Preset execution methods
    private void handlePresetCommand(String command) {
        try {
            // Extract preset name from command
            String presetName = extractPresetName(command);
            if (presetName != null && !presetName.isEmpty()) {
                executePresetByName(presetName);
            } else {
                speak("Please specify which preset to execute");
            }
        } catch (Exception e) {
            speak("Error executing preset");
        }
    }

    private String extractPresetName(String command) {
        // Remove common command prefixes
        String cleanCommand = command.toLowerCase()
                .replace("execute preset", "")
                .replace("run preset", "")
                .replace("start preset", "")
                .trim();

        // Return the remaining text as preset name
        return cleanCommand.isEmpty() ? null : cleanCommand;
    }

    private void executePresetByName(String presetName) {
        new Thread(() -> {
            try {
                // Get database instance
                PresetDatabase database = PresetDatabase.getInstance(this);
                PresetDao presetDao = new PresetDao(database);

                // Search for preset by name (case-insensitive)
                List<Preset> allPresets = presetDao.getAllPresets();
                Preset foundPreset = null;

                for (Preset preset : allPresets) {
                    if (preset.getName().toLowerCase().contains(presetName.toLowerCase())) {
                        foundPreset = preset;
                        break;
                    }
                }

                if (foundPreset != null) {
                    // Execute the preset
                    PresetExecutionService executionService = new PresetExecutionService(this);
                    final Preset presetToExecute = foundPreset;

                    new Handler(Looper.getMainLooper()).post(() -> {
                        speak("Executing preset " + presetToExecute.getName());
                        executionService.executePreset(presetToExecute, new PresetExecutionService.ExecutionCallback() {
                            @Override
                            public void onExecutionStarted(Preset preset) {
                                // Preset started
                            }

                            @Override
                            public void onExecutionProgress(Preset preset, int currentStep, int totalSteps, int progressPercent) {
                                // Progress update
                            }

                            @Override
                            public void onExecutionCompleted(Preset preset) {
                                speak("Preset completed successfully");
                            }

                            @Override
                            public void onExecutionError(Preset preset, String error) {
                                speak("Preset execution failed");
                            }
                        });
                    });
                } else {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        speak("Preset " + presetName + " not found");
                    });
                }
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    speak("Error executing preset");
                });
            }
        }).start();
    }

    private void stopPresetExecution() {
        // Stop any running preset
        sendMotorCommand("S"); // Stop all movements
        speak("Stopping preset execution");

        // TODO: Get reference to active PresetExecutionService and call stopExecution()
        // This would require maintaining a reference to the service instance
    }
}
